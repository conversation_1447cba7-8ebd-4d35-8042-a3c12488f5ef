/* Article Page Specific Styles */

/* Article Header */
.article-header {
    padding: 8rem 0 3rem;
    background: linear-gradient(135deg, var(--primary-cream) 0%, var(--warm-gray) 100%);
}

.article-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.article-category {
    background: var(--accent-rose);
    color: var(--soft-white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.article-date,
.reading-time {
    color: var(--text-light);
    font-size: 0.9rem;
}

.article-title {
    font-size: 3rem;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
    max-width: 800px;
}

.article-subtitle {
    font-size: 1.3rem;
    color: var(--text-medium);
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 700px;
}

.article-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.author-title {
    color: var(--text-medium);
    font-size: 0.9rem;
}

/* Article Hero Image */
.article-hero-image {
    width: 100%;
    height: 500px;
    overflow: hidden;
}

.article-hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Article Content */
.article-content {
    padding: 4rem 0;
    background: var(--soft-white);
}

.content-wrapper {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 4rem;
    max-width: 1200px;
    margin: 0 auto;
}

.article-body {
    max-width: 800px;
}

.lead {
    font-size: 1.3rem;
    line-height: 1.7;
    color: var(--text-medium);
    margin-bottom: 3rem;
    font-style: italic;
}

.article-body h2 {
    font-size: 2rem;
    margin: 3rem 0 1.5rem;
    color: var(--text-dark);
}

.article-body h3 {
    font-size: 1.5rem;
    margin: 2.5rem 0 1rem;
    color: var(--text-dark);
}

.article-body p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: var(--text-medium);
}

.article-body ul,
.article-body ol {
    margin: 1.5rem 0;
    padding-left: 2rem;
}

.article-body li {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 0.5rem;
    color: var(--text-medium);
}

.article-body strong {
    color: var(--text-dark);
    font-weight: 600;
}

/* Article Images */
.article-image {
    margin: 3rem 0;
    text-align: center;
}

.article-image img {
    width: 100%;
    max-width: 800px;
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--gentle-shadow);
}

.image-caption {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-top: 1rem;
    font-style: italic;
}

/* Article Quote */
.article-quote {
    margin: 3rem 0;
    padding: 2rem;
    background: var(--warm-gray);
    border-left: 4px solid var(--accent-rose);
    border-radius: 0 15px 15px 0;
}

.article-quote blockquote {
    font-size: 1.2rem;
    line-height: 1.6;
    color: var(--text-dark);
    font-style: italic;
    margin: 0;
}

/* Article CTA */
.article-cta {
    background: linear-gradient(135deg, var(--accent-rose) 0%, var(--accent-gold) 100%);
    color: var(--soft-white);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    margin: 3rem 0;
}

.article-cta h3 {
    color: var(--soft-white);
    margin-bottom: 1rem;
}

.article-cta p {
    color: var(--soft-white);
    opacity: 0.9;
}

.article-cta a {
    color: var(--soft-white);
    text-decoration: underline;
}

/* Article Tags */
.article-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 3rem 0;
}

.tag {
    background: var(--warm-gray);
    color: var(--text-medium);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.tag:hover {
    background: var(--accent-rose);
    color: var(--soft-white);
}

/* Share Buttons */
.share-buttons {
    margin: 3rem 0;
    padding: 2rem 0;
    border-top: 1px solid var(--warm-gray);
    border-bottom: 1px solid var(--warm-gray);
}

.share-buttons h4 {
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.share-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    color: var(--soft-white);
    font-weight: 500;
    transition: all 0.3s ease;
}

.share-btn.facebook { background: #1877f2; }
.share-btn.instagram { background: #e4405f; }
.share-btn.pinterest { background: #bd081c; }
.share-btn.twitter { background: #1da1f2; }

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Sidebar */
.article-sidebar {
    position: sticky;
    top: 120px;
    height: fit-content;
}

.sidebar-widget {
    background: var(--primary-cream);
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
}

.sidebar-widget h4 {
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

/* Author Widget */
.sidebar-author-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
}

.author-widget {
    text-align: center;
}

.author-widget p {
    color: var(--text-medium);
    margin-bottom: 1.5rem;
}

/* Related Articles Widget */
.related-articles {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.related-article {
    display: flex;
    gap: 1rem;
    text-decoration: none;
    color: inherit;
    padding: 1rem;
    border-radius: 10px;
    transition: background 0.3s ease;
}

.related-article:hover {
    background: var(--warm-gray);
}

.related-article img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    flex-shrink: 0;
}

.related-article h5 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    line-height: 1.3;
}

.related-article span {
    font-size: 0.8rem;
    color: var(--text-light);
}

/* Newsletter Widget */
.sidebar-newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.sidebar-newsletter-form input {
    padding: 0.8rem;
    border: 2px solid var(--warm-gray);
    border-radius: 10px;
    outline: none;
    transition: border-color 0.3s ease;
}

.sidebar-newsletter-form input:focus {
    border-color: var(--accent-rose);
}

.sidebar-newsletter-form .btn {
    padding: 0.8rem;
    font-size: 0.9rem;
}

/* Comments Section */
.comments-section {
    background: var(--warm-gray);
    padding: 4rem 0;
}

.comments-section h3 {
    margin-bottom: 2rem;
    color: var(--text-dark);
}

.comment-form {
    background: var(--soft-white);
    padding: 2rem;
    border-radius: 20px;
    max-width: 600px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--warm-gray);
    border-radius: 10px;
    outline: none;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--accent-rose);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .article-header {
        padding: 6rem 0 2rem;
    }

    .article-title {
        font-size: 2rem;
    }

    .article-subtitle {
        font-size: 1.1rem;
    }

    .article-hero-image {
        height: 300px;
    }

    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .article-sidebar {
        position: static;
        order: -1;
    }

    .sidebar-widget {
        margin-bottom: 1.5rem;
    }

    .share-links {
        justify-content: center;
    }

    .share-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .article-title {
        font-size: 1.8rem;
    }

    .article-body h2 {
        font-size: 1.5rem;
    }

    .article-body h3 {
        font-size: 1.3rem;
    }

    .article-body p,
    .article-body li {
        font-size: 1rem;
    }

    .sidebar-widget {
        padding: 1.5rem;
    }

    .comment-form {
        padding: 1.5rem;
    }
}
