// Article Page Specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Reading progress bar
    function createReadingProgress() {
        const progressBar = document.createElement('div');
        progressBar.className = 'reading-progress';
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-rose), var(--accent-gold));
            z-index: 9999;
            transition: width 0.3s ease;
        `;
        
        document.body.appendChild(progressBar);
        
        window.addEventListener('scroll', () => {
            const article = document.querySelector('.article-body');
            if (!article) return;
            
            const articleTop = article.offsetTop;
            const articleHeight = article.offsetHeight;
            const windowHeight = window.innerHeight;
            const scrollTop = window.pageYOffset;
            
            const articleStart = articleTop - windowHeight / 2;
            const articleEnd = articleTop + articleHeight - windowHeight / 2;
            
            if (scrollTop < articleStart) {
                progressBar.style.width = '0%';
            } else if (scrollTop > articleEnd) {
                progressBar.style.width = '100%';
            } else {
                const progress = ((scrollTop - articleStart) / (articleEnd - articleStart)) * 100;
                progressBar.style.width = progress + '%';
            }
        });
    }

    createReadingProgress();

    // Share buttons functionality
    const shareButtons = document.querySelectorAll('.share-btn');
    
    shareButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent(document.title);
            const platform = this.classList[1]; // facebook, instagram, etc.
            
            let shareUrl = '';
            
            switch(platform) {
                case 'facebook':
                    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
                    break;
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
                    break;
                case 'pinterest':
                    const image = document.querySelector('.article-hero-image img')?.src || '';
                    shareUrl = `https://pinterest.com/pin/create/button/?url=${url}&media=${encodeURIComponent(image)}&description=${title}`;
                    break;
                case 'instagram':
                    // Instagram doesn't have direct sharing, so copy to clipboard
                    navigator.clipboard.writeText(window.location.href).then(() => {
                        showNotification('Odkaz zkopírován! Můžete ho vložit na Instagram.');
                    });
                    return;
            }
            
            if (shareUrl) {
                window.open(shareUrl, 'share', 'width=600,height=400');
            }
            
            // Track sharing
            console.log(`Shared on ${platform}: ${title}`);
        });
    });

    // Comment form handling
    const commentForm = document.querySelector('.comment-form');
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = this.querySelector('#comment-name').value;
            const email = this.querySelector('#comment-email').value;
            const comment = this.querySelector('#comment-text').value;
            
            if (name && email && comment) {
                const button = this.querySelector('button[type="submit"]');
                const originalText = button.textContent;
                
                button.textContent = 'Odesílám...';
                button.disabled = true;
                
                // Simulate form submission
                setTimeout(() => {
                    button.textContent = 'Odesláno!';
                    button.style.background = '#4CAF50';
                    
                    showNotification('Děkujeme za váš komentář! Bude zobrazen po schválení.');
                    
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.disabled = false;
                        button.style.background = '';
                        this.reset();
                    }, 2000);
                }, 1000);
            }
        });
    }

    // Newsletter form in sidebar
    const sidebarNewsletterForm = document.querySelector('.sidebar-newsletter-form');
    if (sidebarNewsletterForm) {
        sidebarNewsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[type="email"]').value;
            
            if (email) {
                const button = this.querySelector('button');
                const originalText = button.textContent;
                
                button.textContent = 'Přihlašuji...';
                button.disabled = true;
                
                setTimeout(() => {
                    button.textContent = 'Děkujeme!';
                    button.style.background = '#4CAF50';
                    
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.disabled = false;
                        button.style.background = '';
                        this.querySelector('input[type="email"]').value = '';
                    }, 2000);
                }, 1000);
            }
        });
    }

    // Smooth scrolling for anchor links within article
    const articleLinks = document.querySelectorAll('.article-body a[href^="#"]');
    articleLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Image loading fixed - ensure all images are visible
    const articleImages = document.querySelectorAll('.article-image img, .article-hero-image img');

    // Make sure all images are visible by default
    articleImages.forEach(img => {
        img.style.opacity = '1';
        img.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

        // Add error handling for broken images
        img.onerror = function() {
            this.style.opacity = '0.5';
            this.alt = 'Obrázek se nepodařilo načíst';
            console.log('Image failed to load:', this.src);
        };

        // Ensure image loads properly
        if (!img.complete) {
            img.onload = () => {
                img.style.opacity = '1';
            };
        }
    });

    // Table of contents generation (if article has many headings)
    function generateTableOfContents() {
        const headings = document.querySelectorAll('.article-body h2, .article-body h3');
        
        if (headings.length > 3) {
            const toc = document.createElement('div');
            toc.className = 'table-of-contents';
            toc.innerHTML = '<h4>Obsah článku</h4><ul></ul>';
            
            const tocList = toc.querySelector('ul');
            
            headings.forEach((heading, index) => {
                const id = `heading-${index}`;
                heading.id = id;
                
                const li = document.createElement('li');
                li.className = heading.tagName.toLowerCase();
                
                const link = document.createElement('a');
                link.href = `#${id}`;
                link.textContent = heading.textContent;
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    heading.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                });
                
                li.appendChild(link);
                tocList.appendChild(li);
            });
            
            // Insert TOC after the lead paragraph
            const lead = document.querySelector('.lead');
            if (lead) {
                lead.insertAdjacentElement('afterend', toc);
            }
        }
    }

    generateTableOfContents();

    // Estimated reading time calculation
    function calculateReadingTime() {
        const articleBody = document.querySelector('.article-body');
        if (!articleBody) return;
        
        const text = articleBody.textContent;
        const wordsPerMinute = 200;
        const words = text.trim().split(/\s+/).length;
        const readingTime = Math.ceil(words / wordsPerMinute);
        
        const readingTimeElement = document.querySelector('.reading-time');
        if (readingTimeElement) {
            readingTimeElement.textContent = `${readingTime} min čtení`;
        }
    }

    calculateReadingTime();

    // Copy link functionality
    function addCopyLinkButton() {
        const shareButtons = document.querySelector('.share-links');
        if (shareButtons) {
            const copyButton = document.createElement('button');
            copyButton.className = 'share-btn copy-link';
            copyButton.style.background = '#666';
            copyButton.innerHTML = '<i class="fas fa-link"></i> Kopírovat odkaz';
            
            copyButton.addEventListener('click', function() {
                navigator.clipboard.writeText(window.location.href).then(() => {
                    this.innerHTML = '<i class="fas fa-check"></i> Zkopírováno!';
                    this.style.background = '#4CAF50';
                    
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-link"></i> Kopírovat odkaz';
                        this.style.background = '#666';
                    }, 2000);
                });
            });
            
            shareButtons.appendChild(copyButton);
        }
    }

    addCopyLinkButton();

    // Print functionality
    function addPrintButton() {
        const shareButtons = document.querySelector('.share-links');
        if (shareButtons) {
            const printButton = document.createElement('button');
            printButton.className = 'share-btn print-article';
            printButton.style.background = '#333';
            printButton.innerHTML = '<i class="fas fa-print"></i> Tisknout';
            
            printButton.addEventListener('click', function() {
                window.print();
            });
            
            shareButtons.appendChild(printButton);
        }
    }

    addPrintButton();

    // Show notification function
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: var(--accent-rose);
            color: var(--soft-white);
            padding: 1rem 1.5rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px var(--soft-shadow);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Track reading engagement
    let readingStartTime = Date.now();
    let maxScrollDepth = 0;
    
    window.addEventListener('scroll', () => {
        const scrollDepth = (window.pageYOffset / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
        maxScrollDepth = Math.max(maxScrollDepth, scrollDepth);
    });
    
    window.addEventListener('beforeunload', () => {
        const readingTime = (Date.now() - readingStartTime) / 1000;
        console.log(`Reading engagement: ${readingTime}s, Max scroll: ${maxScrollDepth.toFixed(1)}%`);
        // In real app: send to analytics
    });

    // Console message
    console.log('%c📖 Článek načten! Užijte si čtení 📖', 'color: #d4a574; font-size: 14px; font-weight: bold;');
});
