<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>P<PERSON><PERSON>padová studie: <PERSON><PERSON><PERSON> × GrowyNET</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #E3F2FD 0%, #F8F9FA 100%);
            color: #2C3E50;
            line-height: 1.6;
        }
        
        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .page-1 {
            background: linear-gradient(135deg, #E3F2FD 0%, #F8F9FA 50%, #E8F5E8 100%);
        }
        
        .header {
            background: linear-gradient(135deg, #208655 0%, #1a6b47 100%);
            color: white;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 300px;
            height: 300px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: -30%;
            left: -10%;
            width: 200px;
            height: 200px;
            background: rgba(255, 210, 96, 0.15);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite reverse;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .header-content {
            position: relative;
            z-index: 2;
        }
        
        .logos {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .logo {
            height: 50px;
            filter: brightness(0) invert(1);
        }
        
        .vs {
            font-size: 24px;
            font-weight: 300;
            opacity: 0.8;
        }
        
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 15px;
            line-height: 1.2;
        }
        
        .subtitle {
            font-size: 18px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .content {
            padding: 50px 40px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #208655;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .icon {
            width: 30px;
            height: 30px;
            background: #208655;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .challenge-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-top: 25px;
        }
        
        .challenge-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-left: 4px solid #FFD260;
            transition: all 0.3s ease;
        }

        .challenge-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .challenge-item h4 {
            color: #208655;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .challenge-item p {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .solution-box {
            background: linear-gradient(135deg, #208655 0%, #1a6b47 100%);
            color: white;
            padding: 35px;
            border-radius: 20px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }
        
        .solution-box::before {
            content: '';
            position: absolute;
            top: -30px;
            right: -30px;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }
        
        .solution-content {
            position: relative;
            z-index: 2;
        }
        
        .solution-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .solution-desc {
            font-size: 16px;
            opacity: 0.95;
            margin-bottom: 25px;
        }
        
        .features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .feature {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }
        
        .feature-icon {
            width: 20px;
            height: 20px;
            background: #FFD260;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #208655;
            font-size: 12px;
            font-weight: bold;
        }
        
        .company-info {
            background: #F8F9FA;
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #E3F2FD;
        }
        
        .company-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .company-logo {
            height: 60px;
        }
        
        .company-details h3 {
            color: #208655;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .company-details p {
            color: #666;
            font-size: 14px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 800;
            color: #208655;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }
        
        @media print {
            body {
                margin: 0;
                background: white;
            }
            .page {
                box-shadow: none;
                margin: 0;
                page-break-after: always;
                background: white !important;
            }
            .page:last-child {
                page-break-after: avoid;
            }
        }

        @page {
            size: A4;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <div style="position: fixed; top: 20px; right: 20px; z-index: 1000; display: none;" id="printBtn">
        <button onclick="window.print()" style="background: #208655; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 600; box-shadow: 0 4px 12px rgba(32, 134, 85, 0.3);">
            🖨️ Tisknout PDF
        </button>
    </div>

    <script>
        // Show print button only on screen
        document.addEventListener('DOMContentLoaded', function() {
            const printBtn = document.getElementById('printBtn');
            if (window.matchMedia && !window.matchMedia('print').matches) {
                printBtn.style.display = 'block';
            }
        });

        // Optimize for print
        window.addEventListener('beforeprint', function() {
            document.body.style.background = 'white';
        });
    </script>
    <!-- STRANA 1 -->
    <div class="page page-1">
        <div class="header">
            <div class="header-content">
                <div class="logos">
                    <img src="assets/drbnalogo.svg" alt="Drbna" class="logo">
                    <span class="vs">×</span>
                    <img src="assets/logogrowy.png" alt="GrowyNET" class="logo">
                </div>
                <h1 class="title">Revoluce v interní komunikaci</h1>
                <p class="subtitle">Jak GrowyNET transformoval komunikaci ve společnosti Drbna a ušetřil 15 hodin týdně</p>
            </div>
        </div>
        
        <div class="content">
            <div class="section">
                <div class="company-info">
                    <div class="company-header">
                        <img src="assets/drbnalogo.svg" alt="Drbna" class="company-logo">
                        <div class="company-details">
                            <h3>Drbna s.r.o.</h3>
                            <p>Moderní mediální společnost specializující se na lifestyle obsah a digitální marketing</p>
                        </div>
                    </div>
                    <div class="stats">
                        <div class="stat">
                            <div class="stat-number">85</div>
                            <div class="stat-label">Zaměstnanců</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">4</div>
                            <div class="stat-label">Pobočky</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">2019</div>
                            <div class="stat-label">Založeno</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2 class="section-title">
                    <div class="icon">⚠️</div>
                    Výzva: Chaos v komunikaci
                </h2>
                <div class="challenge-grid">
                    <div class="challenge-item">
                        <h4>📧 Přetížené e-maily</h4>
                        <p>Důležité informace se ztrácely v záplavě e-mailů. Zaměstnanci přehlíželi klíčové směrnice.</p>
                    </div>
                    <div class="challenge-item">
                        <h4>📱 Nedostupnost non-desk pracovníků</h4>
                        <p>Terénní týmy a pracovníci bez stálého přístupu k PC nedostávali aktuální informace.</p>
                    </div>
                    <div class="challenge-item">
                        <h4>⏰ Pomalé šíření informací</h4>
                        <p>Nové směrnice a oznámení se k zaměstnancům dostávaly s několikadenním zpožděním.</p>
                    </div>
                    <div class="challenge-item">
                        <h4>📊 Chybějící zpětná vazba</h4>
                        <p>Management neměl přehled o tom, kdo informace četl a pochopil.</p>
                    </div>
                </div>
            </div>
            
            <div class="solution-box">
                <div class="solution-content">
                    <h2 class="solution-title">🚀 Řešení: GrowyNET platforma</h2>
                    <p class="solution-desc">Komplexní řešení pro moderní interní komunikaci s důrazem na jednoduchost a dostupnost pro všechny zaměstnance.</p>
                    <div class="features">
                        <div class="feature">
                            <div class="feature-icon">✓</div>
                            <span>Mobilní aplikace pro všechny</span>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">✓</div>
                            <span>Okamžité push notifikace</span>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">✓</div>
                            <span>Sledování přečtení</span>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">✓</div>
                            <span>Interaktivní zpětná vazba</span>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">✓</div>
                            <span>Centralizované směrnice</span>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">✓</div>
                            <span>Rychlý onboarding</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- STRANA 2 -->
    <div class="page" style="page-break-before: always;">
        <div class="content" style="padding-top: 40px;">
            <div class="section">
                <h2 class="section-title">
                    <div class="icon">🎯</div>
                    Implementace s Agentes
                </h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                    <div>
                        <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.08); margin-bottom: 20px;">
                            <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                                <img src="assets/logoagentes.png" alt="Agentes" style="height: 40px;">
                                <div>
                                    <h4 style="color: #208655; font-weight: 600; margin-bottom: 5px;">Technologický partner</h4>
                                    <p style="color: #666; font-size: 14px;">Odborná implementace a customizace</p>
                                </div>
                            </div>
                            <ul style="list-style: none; padding: 0;">
                                <li style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; gap: 10px;">
                                    <span style="color: #208655;">▶</span> Analýza potřeb a návrh řešení
                                </li>
                                <li style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; gap: 10px;">
                                    <span style="color: #208655;">▶</span> Customizace GrowyNET platformy
                                </li>
                                <li style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; gap: 10px;">
                                    <span style="color: #208655;">▶</span> Integrace s existujícími systémy
                                </li>
                                <li style="padding: 8px 0; display: flex; align-items: center; gap: 10px;">
                                    <span style="color: #208655;">▶</span> Školení administrátorů
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <div style="background: linear-gradient(135deg, #FFD260 0%, #FFC107 100%); padding: 25px; border-radius: 15px; color: #2C3E50;">
                            <h4 style="font-weight: 700; margin-bottom: 15px; font-size: 18px;">⚡ Rychlá implementace</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div style="text-align: center;">
                                    <div style="font-size: 32px; font-weight: 800; color: #208655;">14</div>
                                    <div style="font-size: 12px; font-weight: 600;">dní implementace</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-size: 32px; font-weight: 800; color: #208655;">2</div>
                                    <div style="font-size: 12px; font-weight: 600;">hodiny školení</div>
                                </div>
                            </div>
                            <p style="margin-top: 15px; font-size: 14px; line-height: 1.4;">Díky expertize Agentes proběhla implementace rychle a bez komplikací.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">
                    <div class="icon">📈</div>
                    Výsledky a přínosy
                </h2>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 30px;">
                    <div style="background: white; padding: 20px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.08);">
                        <div style="font-size: 36px; font-weight: 800; color: #208655; margin-bottom: 8px;">98%</div>
                        <div style="font-size: 12px; color: #666; font-weight: 600;">Míra přečtení zpráv</div>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.08);">
                        <div style="font-size: 36px; font-weight: 800; color: #208655; margin-bottom: 8px;">15h</div>
                        <div style="font-size: 12px; color: #666; font-weight: 600;">Úspora času týdně</div>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.08);">
                        <div style="font-size: 36px; font-weight: 800; color: #208655; margin-bottom: 8px;">85%</div>
                        <div style="font-size: 12px; color: #666; font-weight: 600;">Spokojenost zaměstnanců</div>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 12px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.08);">
                        <div style="font-size: 36px; font-weight: 800; color: #208655; margin-bottom: 8px;">3min</div>
                        <div style="font-size: 12px; color: #666; font-weight: 600;">Průměrný čas odezvy</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px;">
                    <div style="background: #F8F9FA; padding: 25px; border-radius: 15px; border-left: 4px solid #208655;">
                        <h4 style="color: #208655; font-weight: 600; margin-bottom: 15px;">💼 Přínosy pro management</h4>
                        <ul style="list-style: none; padding: 0; color: #666; font-size: 14px; line-height: 1.6;">
                            <li style="margin-bottom: 8px;">✓ Okamžitý přehled o přečtení zpráv</li>
                            <li style="margin-bottom: 8px;">✓ Rychlé šíření důležitých informací</li>
                            <li style="margin-bottom: 8px;">✓ Centralizovaná správa směrnic</li>
                            <li style="margin-bottom: 8px;">✓ Detailní analytics a reporty</li>
                        </ul>
                    </div>
                    <div style="background: #F8F9FA; padding: 25px; border-radius: 15px; border-left: 4px solid #FFD260;">
                        <h4 style="color: #208655; font-weight: 600; margin-bottom: 15px;">👥 Přínosy pro zaměstnance</h4>
                        <ul style="list-style: none; padding: 0; color: #666; font-size: 14px; line-height: 1.6;">
                            <li style="margin-bottom: 8px;">✓ Přístup k informacím kdykoliv</li>
                            <li style="margin-bottom: 8px;">✓ Intuitivní mobilní aplikace</li>
                            <li style="margin-bottom: 8px;">✓ Možnost zpětné vazby</li>
                            <li style="margin-bottom: 8px;">✓ Žádné zmeškané důležité zprávy</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div style="background: linear-gradient(135deg, #208655 0%, #1a6b47 100%); color: white; padding: 30px; border-radius: 20px; margin: 30px 0; text-align: center;">
                <h3 style="font-size: 24px; font-weight: 700; margin-bottom: 15px;">💬 Testimonial</h3>
                <p style="font-size: 16px; font-style: italic; margin-bottom: 20px; opacity: 0.95;">"GrowyNET kompletně změnil způsob, jakým komunikujeme s našimi zaměstnanci. Konečně máme jistotu, že se důležité informace dostanou ke všem včas. Implementace s Agentes byla rychlá a bezproblémová."</p>
                <div style="font-weight: 600;">Jana Nováková, HR ředitelka, Drbna s.r.o.</div>
            </div>

            <div style="background: #F8F9FA; padding: 30px; border-radius: 15px; text-align: center; border: 2px solid #E3F2FD;">
                <h3 style="color: #208655; font-size: 20px; font-weight: 700; margin-bottom: 20px;">🚀 Začněte i vy!</h3>
                <p style="color: #666; margin-bottom: 25px; font-size: 14px;">Transformujte komunikaci ve vaší společnosti s GrowyNET a odbornou podporou Agentes</p>
                <div style="display: flex; justify-content: center; gap: 30px; align-items: center;">
                    <div style="text-align: center;">
                        <img src="assets/logogrowy.png" alt="GrowyNET" style="height: 40px; margin-bottom: 10px;">
                        <div style="font-size: 12px; color: #666;">www.growynet.cz</div>
                        <div style="font-size: 12px; color: #666;"><EMAIL></div>
                    </div>
                    <div style="font-size: 24px; color: #208655;">×</div>
                    <div style="text-align: center;">
                        <img src="assets/logoagentes.png" alt="Agentes" style="height: 40px; margin-bottom: 10px;">
                        <div style="font-size: 12px; color: #666;">www.agentes.cz</div>
                        <div style="font-size: 12px; color: #666;"><EMAIL></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
