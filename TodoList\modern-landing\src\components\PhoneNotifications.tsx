'use client';

import { useState, useEffect } from 'react';

interface Notification {
  id: number;
  title: string;
  message: string;
  icon: string;
  color: string;
  time: string;
}

const notifications: Notification[] = [
  {
    id: 1,
    title: '<PERSON><PERSON><PERSON> přijata',
    message: '<PERSON><PERSON><PERSON><PERSON><PERSON> jste 2,450 Kč',
    icon: '💳',
    color: 'bg-green-500',
    time: 'právě teď'
  },
  {
    id: 2,
    title: 'Investice rostou',
    message: '+12.5% tento měsíc',
    icon: '📈',
    color: 'bg-blue-500',
    time: '2 min'
  },
  {
    id: 3,
    title: 'Bezpečnostní upozornění',
    message: 'Nové přihlášení z iPhone',
    icon: '🛡️',
    color: 'bg-orange-500',
    time: '5 min'
  },
  {
    id: 4,
    title: '<PERSON><PERSON><PERSON><PERSON> převod',
    message: 'Převod dokončen za 0.3s',
    icon: '⚡',
    color: 'bg-purple-500',
    time: '8 min'
  }
];

export default function PhoneNotifications() {
  const [currentNotification, setCurrentNotification] = useState(0);
  const [showNotification, setShowNotification] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setShowNotification(true);
      
      setTimeout(() => {
        setShowNotification(false);
        setTimeout(() => {
          setCurrentNotification((prev) => (prev + 1) % notifications.length);
        }, 300);
      }, 3000);
    }, 4000);

    // Show first notification after 1 second
    setTimeout(() => {
      setShowNotification(true);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const notification = notifications[currentNotification];

  return (
    <div className="relative">
      {/* Phone Frame */}
      <div className="relative w-80 h-[600px] bg-gray-900 rounded-[3rem] p-2 shadow-2xl">
        {/* Phone Screen */}
        <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
          {/* Status Bar */}
          <div className="flex justify-between items-center px-6 py-3 bg-gray-50">
            <span className="text-sm font-semibold">9:41</span>
            <div className="flex items-center space-x-1">
              <div className="w-4 h-2 bg-gray-300 rounded-sm"></div>
              <div className="w-6 h-3 border border-gray-300 rounded-sm">
                <div className="w-4 h-full bg-green-500 rounded-sm"></div>
              </div>
            </div>
          </div>

          {/* App Interface */}
          <div className="p-6 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Dobrý den!</h2>
                <p className="text-gray-600">Zde je váš přehled</p>
              </div>
              <div className="w-10 h-10 bg-emerald-600 rounded-full flex items-center justify-center">
                <span className="text-white">🔔</span>
              </div>
            </div>

            {/* Balance Card */}
            <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-2xl p-6 text-white">
              <p className="text-emerald-100 text-sm">Celkový zůstatek</p>
              <p className="text-3xl font-bold">124,567 Kč</p>
              <div className="flex items-center mt-2">
                <span className="mr-1">📈</span>
                <span className="text-sm">+2.5% tento týden</span>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-xl p-4 text-center hover:scale-105 transition-transform cursor-pointer">
                <div className="w-8 h-8 bg-blue-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                  <span className="text-white text-sm">💳</span>
                </div>
                <p className="text-sm font-medium">Převést</p>
              </div>
              <div className="bg-gray-50 rounded-xl p-4 text-center hover:scale-105 transition-transform cursor-pointer">
                <div className="w-8 h-8 bg-purple-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                  <span className="text-white text-sm">📈</span>
                </div>
                <p className="text-sm font-medium">Investovat</p>
              </div>
            </div>

            {/* Recent Transactions */}
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900">Nedávné transakce</h3>
              <div className="space-y-2">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                      <div>
                        <p className="text-sm font-medium">Transakce #{i}</p>
                        <p className="text-xs text-gray-500">Dnes</p>
                      </div>
                    </div>
                    <span className="text-sm font-semibold">-{i * 150} Kč</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Dynamic Island / Notch */}
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-gray-900 rounded-full"></div>
      </div>

      {/* Floating Notifications */}
      {showNotification && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-72 bg-white rounded-2xl shadow-2xl border border-gray-100 p-4 z-10 animate-bounce">
          <div className="flex items-start space-x-3">
            <div className={`w-10 h-10 ${notification.color} rounded-full flex items-center justify-center text-white flex-shrink-0`}>
              <span className="text-lg">{notification.icon}</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold text-gray-900 truncate">
                {notification.title}
              </p>
              <p className="text-sm text-gray-600 mt-1">
                {notification.message}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                {notification.time}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Floating Elements */}
      <div className="absolute -right-8 top-20 w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center shadow-lg animate-pulse">
        <span className="text-2xl text-emerald-600">💳</span>
      </div>

      <div className="absolute -left-8 bottom-32 w-14 h-14 bg-blue-100 rounded-xl flex items-center justify-center shadow-lg animate-bounce">
        <span className="text-xl text-blue-600">📈</span>
      </div>
    </div>
  );
}
