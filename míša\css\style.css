/* <PERSON><PERSON><PERSON>'s Fashion Blog - Modern Animated Design */

:root {
    --primary-cream: #faf9f7;
    --soft-white: #ffffff;
    --warm-gray: #f5f4f2;
    --text-dark: #2c2c2c;
    --text-medium: #666666;
    --text-light: #999999;
    --accent-rose: #d4a574;
    --accent-gold: #c9a96e;
    --soft-shadow: rgba(0, 0, 0, 0.08);
    --gentle-shadow: rgba(0, 0, 0, 0.04);
    --hover-shadow: rgba(0, 0, 0, 0.12);
    --gradient-1: linear-gradient(135deg, #faf9f7 0%, #f5f4f2 50%, #d4a574 100%);
    --gradient-2: linear-gradient(45deg, #d4a574 0%, #c9a96e 50%, #faf9f7 100%);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.8s ease, visibility 0.8s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-logo {
    font-family: 'Playfair Display', serif;
    font-size: 4rem;
    color: var(--text-dark);
    margin-bottom: 2rem;
    animation: logoFloat 2s ease-in-out infinite alternate;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 3px solid var(--warm-gray);
    border-top: 3px solid var(--accent-rose);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 2rem;
    font-size: 1.2rem;
    color: var(--text-medium);
    animation: textPulse 1.5s ease-in-out infinite alternate;
}

/* Floating Particles */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

.particle {
    position: absolute;
    background: var(--accent-rose);
    border-radius: 50%;
    opacity: 0.1;
    animation: float 20s infinite linear;
}

/* Custom Cursor */
.custom-cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: var(--accent-rose);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.1s ease;
    opacity: 0.7;
    mix-blend-mode: difference;
}

.custom-cursor.hover {
    transform: scale(2);
    background: var(--accent-gold);
}

/* Scroll Progress */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 4px;
    background: var(--gradient-2);
    z-index: 9999;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px var(--accent-rose);
}

/* Modern Keyframes */
@keyframes logoFloat {
    0% { transform: translateY(0px) scale(1); }
    100% { transform: translateY(-10px) scale(1.05); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes textPulse {
    0% { opacity: 0.6; }
    100% { opacity: 1; }
}

@keyframes float {
    0% {
        transform: translateY(100vh) translateX(0px) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.1;
    }
    90% {
        opacity: 0.1;
    }
    100% {
        transform: translateY(-100px) translateX(100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.6s ease-out forwards;
}

.animate-slide-in-down {
    animation: slideInDown 0.8s ease-out forwards;
}

/* Staggered Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* Initially Hidden for Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Ensure images are always visible */
img {
    opacity: 1 !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.article-image img,
.article-hero-image img,
.hero-image img,
.article-card img {
    opacity: 1 !important;
    display: block !important;
    visibility: visible !important;
    max-width: 100%;
    height: auto;
    background: #f9f9f9;
}

/* Fix for broken images */
img[src*="unsplash"] {
    opacity: 1 !important;
    display: block !important;
}

/* Hover effects that don't interfere with visibility */
.article-card:hover img,
.hero-image:hover img {
    opacity: 1 !important;
    visibility: visible !important;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--primary-cream);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 400;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }

p {
    margin-bottom: 1.2rem;
    font-size: 1.1rem;
    line-height: 1.7;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(250, 249, 247, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-bottom: 1px solid rgba(212, 165, 116, 0.1);
    transform: translateY(-100%);
    animation: slideInDown 1s ease-out 0.5s forwards;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    font-weight: 400;
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s ease;
}

.logo:hover {
    color: var(--accent-rose);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-medium);
    font-weight: 400;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--accent-rose);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-rose);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Mobile Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 2px;
    background: var(--text-dark);
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-1);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-2);
    opacity: 0;
    transition: opacity 2s ease;
    animation: gradientShift 8s ease-in-out infinite alternate;
}

@keyframes gradientShift {
    0% { opacity: 0; }
    50% { opacity: 0.1; }
    100% { opacity: 0; }
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text {
    z-index: 2;
}

.hero-title {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--text-dark), var(--accent-rose), var(--accent-gold), var(--text-dark));
    background-size: 300% 300%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1.2;
    animation: textShimmer 4s ease-in-out infinite;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-medium);
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

.hero-image {
    position: relative;
    z-index: 1;
}

.hero-image img {
    width: 100%;
    height: 600px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 60px var(--soft-shadow);
    transition: all 0.3s ease;
    opacity: 1 !important;
    display: block !important;
}

.hero-image img:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 30px 80px var(--hover-shadow);
    opacity: 1 !important;
    visibility: visible !important;
}

@keyframes heroImageFloat {
    0% { transform: translateY(0px) rotateY(0deg); }
    100% { transform: translateY(-8px) rotateY(2deg); }
}

@keyframes ripple {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 300px;
        height: 300px;
        opacity: 0;
    }
}

@keyframes morphGradient {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes textShimmer {
    0% {
        background-position: -200% center;
    }
    100% {
        background-position: 200% center;
    }
}

/* Modern Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2.5rem;
    background: var(--gradient-2);
    background-size: 200% 200%;
    color: var(--soft-white);
    text-decoration: none;
    border-radius: 50px;
    font-weight: 500;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: none;
    cursor: pointer;
    font-size: 1rem;
    box-shadow: 0 8px 25px rgba(212, 165, 116, 0.3);
    position: relative;
    overflow: hidden;
    animation: morphGradient 3s ease infinite;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 40px rgba(212, 165, 116, 0.5);
    animation-play-state: paused;
}

.btn:active {
    transform: translateY(-1px) scale(1.02);
}

.btn-outline {
    background: transparent;
    color: var(--accent-rose);
    border: 2px solid var(--accent-rose);
    box-shadow: none;
}

.btn-outline:hover {
    background: var(--accent-rose);
    color: var(--soft-white);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Section Spacing */
.section {
    padding: 6rem 0;
}

/* Latest Articles */
.latest-articles {
    background: var(--soft-white);
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
    font-size: 2.5rem;
    color: var(--text-dark);
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.article-card {
    background: var(--soft-white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px var(--gentle-shadow);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-decoration: none;
    color: inherit;
    position: relative;
}

.article-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-2);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
}

.article-card:hover::before {
    opacity: 0.05;
}

.article-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 60px var(--hover-shadow);
}

/* 3D Transform Support */
.article-card,
.category-card,
.service-card {
    transform-style: preserve-3d;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.article-card * {
    position: relative;
    z-index: 2;
}

.article-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.article-content {
    padding: 2rem;
}

.article-date {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.article-title {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.article-excerpt {
    color: var(--text-medium);
    line-height: 1.6;
}

/* Categories */
.categories {
    background: var(--warm-gray);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.category-card {
    background: var(--soft-white);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px var(--gentle-shadow);
}

.category-card:hover {
    transform: translateY(-15px) scale(1.05) rotateY(5deg);
    box-shadow: 0 25px 60px var(--hover-shadow);
}

.category-card:hover .category-icon {
    transform: scale(1.3) rotate(15deg);
    animation: iconBounce 0.6s ease;
}

@keyframes iconBounce {
    0%, 20%, 60%, 100% {
        transform: scale(1.3) rotate(15deg) translateY(0);
    }
    40% {
        transform: scale(1.4) rotate(20deg) translateY(-5px);
    }
    80% {
        transform: scale(1.35) rotate(18deg) translateY(-2px);
    }
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--accent-rose);
}

.category-title {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.category-count {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--soft-white);
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1.5rem;
    color: var(--accent-rose);
}

.footer-section p,
.footer-section a {
    color: #cccccc;
    text-decoration: none;
    line-height: 1.8;
}

.footer-section a:hover {
    color: var(--accent-rose);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--accent-rose);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    color: var(--soft-white);
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--accent-gold);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #444;
    padding-top: 2rem;
    text-align: center;
    color: #999;
}

/* About Preview */
.about-preview {
    background: var(--soft-white);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-text h2 {
    margin-bottom: 2rem;
    color: var(--text-dark);
}

.about-text p {
    font-size: 1.2rem;
    color: var(--text-medium);
    margin-bottom: 1.5rem;
}

/* Newsletter */
.newsletter {
    background: linear-gradient(135deg, var(--accent-rose) 0%, var(--accent-gold) 100%);
    color: var(--soft-white);
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter h2 {
    color: var(--soft-white);
    margin-bottom: 1rem;
}

.newsletter p {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    outline: none;
}

.newsletter-form .btn {
    background: var(--soft-white);
    color: var(--accent-rose);
    white-space: nowrap;
}

.newsletter-form .btn:hover {
    background: var(--primary-cream);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--soft-white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 3rem;
        transition: left 0.3s ease;
        box-shadow: 0 5px 20px var(--soft-shadow);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-image img {
        height: 400px;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .newsletter-form {
        flex-direction: column;
        gap: 1rem;
    }

    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.5rem; }
}

/* Legal Pages */
.legal-header {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, var(--primary-cream) 0%, var(--warm-gray) 100%);
    text-align: center;
}

.legal-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.legal-header p {
    font-size: 1.2rem;
    color: var(--text-medium);
}

.legal-content {
    background: var(--soft-white);
}

.legal-wrapper {
    max-width: 800px;
    margin: 0 auto;
}

.legal-wrapper h2 {
    color: var(--text-dark);
    margin: 2.5rem 0 1rem;
    font-size: 1.5rem;
}

.legal-wrapper p {
    color: var(--text-medium);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.legal-wrapper ul {
    margin: 1rem 0 1.5rem 2rem;
}

.legal-wrapper li {
    color: var(--text-medium);
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .legal-header {
        padding: 6rem 0 3rem;
    }

    .legal-header h1 {
        font-size: 2.5rem;
    }

    .legal-wrapper {
        padding: 0 1rem;
    }
}

/* Floating Action Button */
.floating-action-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: var(--gradient-2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--soft-white);
    font-size: 1.5rem;
    box-shadow: 0 8px 25px rgba(212, 165, 116, 0.4);
    cursor: pointer;
    z-index: 1000;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation: fabFloat 3s ease-in-out infinite alternate;
}

.floating-action-btn:hover {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 12px 35px rgba(212, 165, 116, 0.6);
}

@keyframes fabFloat {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-5px); }
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--accent-rose);
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    color: var(--soft-white);
    cursor: pointer;
    z-index: 999;
    transition: all 0.3s ease;
    opacity: 0;
}

.scroll-to-top:hover {
    background: var(--accent-gold);
    transform: translateY(-3px);
}
