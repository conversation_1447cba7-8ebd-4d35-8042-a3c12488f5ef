'use client';

import { useState, useEffect } from 'react';

export default function Home() {
  const [currentNotification, setCurrentNotification] = useState(0);

  const notifications = [
    {
      title: "Nov<PERSON> zpráva",
      message: "Máte 3 nové zprávy od týmu",
      time: "právě teď",
      icon: "💬",
      color: "bg-blue-500"
    },
    {
      title: "Úkol dokončen",
      message: "Projekt byl úspěšně dokončen",
      time: "před 2 min",
      icon: "✅",
      color: "bg-green-500"
    },
    {
      title: "Nový člen týmu",
      message: "<PERSON> se připojil k projektu",
      time: "před 5 min",
      icon: "👤",
      color: "bg-purple-500"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentNotification((prev) => (prev + 1) % notifications.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <main className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">G</span>
              </div>
              <span className="text-xl font-bold text-gray-900">GrowyNet</span>
            </div>

            <div className="hidden md:flex items-center space-x-8">
              <a href="#" className="text-gray-600 hover:text-emerald-600 transition-colors font-medium">Funkce</a>
              <a href="#" className="text-gray-600 hover:text-emerald-600 transition-colors font-medium">Ceny</a>
              <a href="#" className="text-gray-600 hover:text-emerald-600 transition-colors font-medium">O nás</a>
              <a href="#" className="text-gray-600 hover:text-emerald-600 transition-colors font-medium">Kontakt</a>
            </div>

            <div className="flex items-center space-x-4">
              <button className="text-gray-600 hover:text-gray-900 transition-colors font-medium">
                Přihlásit se
              </button>
              <button className="bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-all duration-300 hover:scale-105 font-medium">
                Začít zdarma
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-emerald-50 via-white to-blue-50 py-20">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Vaší zaměstnanci
                  <br />
                  <span className="text-emerald-600">na dosah</span>
                </h1>

                <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                  Moderní platforma pro správu zaměstnanců a projektů,
                  která vám pomůže řídit váš tým efektivně a transparentně.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-emerald-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-emerald-700 hover:scale-105 transition-all duration-300 flex items-center justify-center group shadow-lg">
                  Začít zdarma
                  <span className="ml-2 group-hover:translate-x-1 transition-transform">→</span>
                </button>

                <button className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-xl font-semibold hover:bg-gray-50 hover:scale-105 transition-all duration-300 flex items-center justify-center group">
                  <span className="mr-2">▶</span>
                  Sledovat demo
                </button>
              </div>

              <div className="flex items-center space-x-6 text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Bezpečné</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span>Rychlé</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                  <span>Spolehlivé</span>
                </div>
              </div>
            </div>

            {/* Right Content - Phone Mockup */}
            <div className="relative flex justify-center lg:justify-end">
              {/* Main Phone */}
              <div className="relative w-80 h-[600px] bg-gray-900 rounded-[3rem] p-2 shadow-2xl hover:scale-105 transition-transform duration-500">
                <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
                  {/* Status Bar */}
                  <div className="flex justify-between items-center px-6 py-3 bg-gray-50">
                    <span className="text-sm font-semibold">9:41</span>
                    <div className="flex items-center space-x-1">
                      <div className="w-4 h-2 bg-gray-300 rounded-sm"></div>
                      <div className="w-6 h-3 border border-gray-300 rounded-sm">
                        <div className="w-4 h-full bg-green-500 rounded-sm"></div>
                      </div>
                    </div>
                  </div>

                  {/* App Interface */}
                  <div className="p-6 space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900">Dobrý den!</h2>
                        <p className="text-gray-600">Zde je váš přehled</p>
                      </div>
                      <div className="w-10 h-10 bg-emerald-600 rounded-full flex items-center justify-center">
                        <span className="text-white">🔔</span>
                      </div>
                    </div>

                    {/* Stats Cards */}
                    <div className="space-y-4">
                      <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-2xl p-6 text-white">
                        <p className="text-emerald-100 text-sm">Aktivní zaměstnanci</p>
                        <p className="text-3xl font-bold">247</p>
                        <div className="flex items-center mt-2">
                          <span className="mr-1">📈</span>
                          <span className="text-sm">+12% tento měsíc</span>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-blue-50 rounded-xl p-4">
                          <div className="text-2xl font-bold text-blue-600">89%</div>
                          <div className="text-sm text-blue-600">Produktivita</div>
                        </div>
                        <div className="bg-purple-50 rounded-xl p-4">
                          <div className="text-2xl font-bold text-purple-600">24</div>
                          <div className="text-sm text-purple-600">Projekty</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Dynamic Island */}
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-gray-900 rounded-full"></div>
                </div>
              </div>

              {/* Floating Notification */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-80 bg-white rounded-2xl shadow-2xl border border-gray-100 p-4 z-10 transition-all duration-500 hover:scale-105">
                <div className="flex items-start space-x-3">
                  <div className={`w-10 h-10 ${notifications[currentNotification].color} rounded-full flex items-center justify-center text-white flex-shrink-0`}>
                    <span className="text-lg">{notifications[currentNotification].icon}</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-gray-900 truncate">
                      {notifications[currentNotification].title}
                    </p>
                    <p className="text-sm text-gray-600 mt-1">
                      {notifications[currentNotification].message}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {notifications[currentNotification].time}
                    </p>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -right-8 top-20 w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center shadow-lg animate-pulse hover:scale-110 transition-transform cursor-pointer">
                <span className="text-2xl text-emerald-600">📊</span>
              </div>

              <div className="absolute -left-8 bottom-32 w-14 h-14 bg-blue-100 rounded-xl flex items-center justify-center shadow-lg animate-bounce hover:scale-110 transition-transform cursor-pointer">
                <span className="text-xl text-blue-600">👥</span>
              </div>

              <div className="absolute -right-4 bottom-20 w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center shadow-lg animate-pulse hover:scale-110 transition-transform cursor-pointer">
                <span className="text-lg text-purple-600">⚡</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Logo Section */}
      <section className="py-16 bg-white border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-12">
            <p className="text-gray-500 font-medium">Důvěřují nám přední společnosti</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60 hover:opacity-100 transition-opacity duration-300">
            {[
              { name: "Microsoft", logo: "🏢" },
              { name: "Google", logo: "🔍" },
              { name: "Apple", logo: "🍎" },
              { name: "Amazon", logo: "📦" },
              { name: "Meta", logo: "👥" },
              { name: "Netflix", logo: "🎬" }
            ].map((company, index) => (
              <div key={index} className="flex flex-col items-center space-y-2 group hover:scale-110 transition-transform duration-300">
                <div className="text-4xl group-hover:scale-125 transition-transform duration-300">
                  {company.logo}
                </div>
                <span className="text-sm font-medium text-gray-600 group-hover:text-emerald-600 transition-colors">
                  {company.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Main Content Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Moderní řízení pro moderní
              <br />
              <span className="text-emerald-600">podnikání</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              GrowyNet vám pomůže efektivně řídit zaměstnance, projekty a procesy
              s využitím nejmodernějších technologií a intuitivního rozhraní.
            </p>
          </div>

          {/* Feature Grid */}
          <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
            {/* Left - Image/Mockup */}
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8 hover:scale-105 transition-transform duration-500">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-2xl font-bold text-gray-900">Týmový přehled</h3>
                    <div className="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center">
                      <span className="text-white text-sm">👥</span>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {[
                      { name: "Jan Novák", role: "Frontend Developer", status: "online", avatar: "👨‍💻" },
                      { name: "Marie Svobodová", role: "UX Designer", status: "busy", avatar: "👩‍🎨" },
                      { name: "Petr Dvořák", role: "Project Manager", status: "away", avatar: "👨‍💼" }
                    ].map((member, index) => (
                      <div key={index} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div className="text-2xl">{member.avatar}</div>
                        <div className="flex-1">
                          <div className="font-semibold text-gray-900">{member.name}</div>
                          <div className="text-sm text-gray-600">{member.role}</div>
                        </div>
                        <div className={`w-3 h-3 rounded-full ${
                          member.status === 'online' ? 'bg-green-500' :
                          member.status === 'busy' ? 'bg-red-500' : 'bg-yellow-500'
                        } animate-pulse`}></div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Floating Stats */}
              <div className="absolute -top-4 -right-4 bg-emerald-600 text-white p-4 rounded-xl shadow-lg animate-bounce">
                <div className="text-2xl font-bold">98%</div>
                <div className="text-sm">Spokojenost</div>
              </div>
            </div>

            {/* Right - Content */}
            <div className="space-y-8">
              <div className="space-y-6">
                <h3 className="text-3xl font-bold text-gray-900">
                  Mějte svůj tým vždy pod kontrolou
                </h3>
                <p className="text-lg text-gray-600 leading-relaxed">
                  S GrowyNet máte přehled o všech zaměstnancích v reálném čase.
                  Sledujte produktivitu, komunikujte efektivně a řiďte projekty
                  s naprostou lehkostí.
                </p>
              </div>

              <div className="space-y-4">
                {[
                  { icon: "⚡", title: "Rychlé nasazení", desc: "Spuštění za méně než 5 minut" },
                  { icon: "🔒", title: "Bezpečnost na prvním místě", desc: "Enterprise-grade zabezpečení" },
                  { icon: "📊", title: "Pokročilé analýzy", desc: "Detailní reporty a statistiky" }
                ].map((feature, index) => (
                  <div key={index} className="flex items-start space-x-4 group hover:scale-105 transition-transform duration-300">
                    <div className="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center group-hover:bg-emerald-600 transition-colors">
                      <span className="text-xl group-hover:scale-125 transition-transform">{feature.icon}</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors">{feature.title}</h4>
                      <p className="text-gray-600">{feature.desc}</p>
                    </div>
                  </div>
                ))}
              </div>

              <button className="bg-emerald-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-emerald-700 hover:scale-105 transition-all duration-300 flex items-center group shadow-lg">
                Vyzkoušet zdarma
                <span className="ml-2 group-hover:translate-x-1 transition-transform">→</span>
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Digitální transformace,
              <br />
              <span className="text-emerald-600">pro vaše potřeby</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Naše platforma přináší měřitelné výsledky, které pomohou vašemu podnikání
              růst rychleji a efektivněji než kdy předtím.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-20">
            {[
              {
                value: '30%',
                label: 'Rychlejší procesy',
                description: 'Oproti tradičním metodám',
                color: 'from-emerald-500 to-emerald-600',
                icon: '⚡'
              },
              {
                value: '93%',
                label: 'Spokojenost klientů',
                description: 'Na základě hodnocení',
                color: 'from-blue-500 to-blue-600',
                icon: '😊'
              },
              {
                value: '55%',
                label: 'Úspora času',
                description: 'Při správě týmu',
                color: 'from-purple-500 to-purple-600',
                icon: '⏰'
              }
            ].map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl hover:scale-105 transition-all duration-500 border border-gray-100">
                  <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <span className="text-2xl text-white">{stat.icon}</span>
                  </div>

                  <div className="text-5xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors">
                    {stat.value}
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {stat.label}
                  </h3>

                  <p className="text-gray-600">
                    {stat.description}
                  </p>

                  <div className="h-1 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full mt-6 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Grid Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Vše co potřebujete
              <br />
              <span className="text-emerald-600">na jednom místě</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Objevte sílu moderních nástrojů pro řízení týmu, které vám pomohou
              dosáhnout lepších výsledků s menším úsilím.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: '👥',
                title: 'Správa týmu',
                description: 'Kompletní přehled o všech zaměstnancích, jejich rolích a odpovědnostech v reálném čase.',
                color: 'bg-blue-500',
                gradient: 'from-blue-50 to-blue-100'
              },
              {
                icon: '📊',
                title: 'Analýzy a reporty',
                description: 'Pokročilé analytické nástroje pro sledování výkonnosti a produktivity vašeho týmu.',
                color: 'bg-emerald-500',
                gradient: 'from-emerald-50 to-emerald-100'
              },
              {
                icon: '💬',
                title: 'Komunikace',
                description: 'Integrované komunikační nástroje pro efektivní spolupráci a sdílení informací.',
                color: 'bg-purple-500',
                gradient: 'from-purple-50 to-purple-100'
              },
              {
                icon: '📅',
                title: 'Plánování projektů',
                description: 'Intuitivní nástroje pro plánování, sledování a řízení projektů od začátku do konce.',
                color: 'bg-orange-500',
                gradient: 'from-orange-50 to-orange-100'
              },
              {
                icon: '🔒',
                title: 'Bezpečnost',
                description: 'Enterprise-grade zabezpečení s pokročilým šifrováním a kontrolou přístupu.',
                color: 'bg-red-500',
                gradient: 'from-red-50 to-red-100'
              },
              {
                icon: '⚡',
                title: 'Automatizace',
                description: 'Automatizujte rutinní úkoly a procesy pro zvýšení efektivity a úsporu času.',
                color: 'bg-yellow-500',
                gradient: 'from-yellow-50 to-yellow-100'
              }
            ].map((feature, index) => (
              <div key={index} className="group hover:-translate-y-2 transition-all duration-500">
                <div className={`bg-gradient-to-br ${feature.gradient} rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 h-full`}>
                  <div className={`w-16 h-16 ${feature.color} rounded-2xl flex items-center justify-center text-white mb-6 group-hover:shadow-lg transition-all duration-300 hover:scale-110 hover:rotate-3`}>
                    <span className="text-2xl">{feature.icon}</span>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors">
                    {feature.title}
                  </h3>

                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>

                  <div className="h-1 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full mt-6 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Vyzkoušejte naši aplikaci
              <br />
              <span className="text-emerald-600">zdarma</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Připojte se k tisícům spokojených uživatelů, kteří již využívají
              GrowyNet pro efektivní řízení svých týmů a projektů.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left - Testimonial */}
            <div className="space-y-8">
              <div className="bg-gradient-to-br from-emerald-50 to-blue-50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500">
                <div className="flex items-start space-x-4 mb-6">
                  <div className="w-16 h-16 bg-emerald-600 rounded-full flex items-center justify-center text-white text-2xl">
                    👨‍💼
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 text-lg">Martin Novák</h4>
                    <p className="text-gray-600">CEO, TechStart s.r.o.</p>
                    <div className="flex text-yellow-400 mt-1">
                      {'★'.repeat(5)}
                    </div>
                  </div>
                </div>

                <blockquote className="text-lg text-gray-700 leading-relaxed italic">
                  "GrowyNet nám pomohl zvýšit produktivitu týmu o 40%.
                  Konečně máme přehled o všech projektech na jednom místě
                  a komunikace mezi týmy je mnohem efektivnější."
                </blockquote>
              </div>

              <div className="grid grid-cols-3 gap-4">
                {[
                  { metric: "40%", label: "Vyšší produktivita" },
                  { metric: "60%", label: "Rychlejší projekty" },
                  { metric: "95%", label: "Spokojenost týmu" }
                ].map((item, index) => (
                  <div key={index} className="text-center p-4 bg-gray-50 rounded-xl hover:bg-emerald-50 transition-colors duration-300">
                    <div className="text-2xl font-bold text-emerald-600">{item.metric}</div>
                    <div className="text-sm text-gray-600">{item.label}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right - App Screenshots */}
            <div className="relative">
              <div className="grid grid-cols-2 gap-4">
                {/* Screenshot 1 */}
                <div className="bg-white rounded-2xl shadow-lg p-6 hover:scale-105 transition-transform duration-500">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-bold text-gray-900">Dashboard</h4>
                      <div className="w-6 h-6 bg-emerald-600 rounded-full"></div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-3 bg-emerald-200 rounded-full"></div>
                      <div className="h-3 bg-blue-200 rounded-full w-3/4"></div>
                      <div className="h-3 bg-purple-200 rounded-full w-1/2"></div>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="h-8 bg-gray-100 rounded"></div>
                      <div className="h-8 bg-gray-100 rounded"></div>
                    </div>
                  </div>
                </div>

                {/* Screenshot 2 */}
                <div className="bg-white rounded-2xl shadow-lg p-6 hover:scale-105 transition-transform duration-500 mt-8">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-bold text-gray-900">Týmy</h4>
                      <div className="w-6 h-6 bg-blue-600 rounded-full"></div>
                    </div>
                    <div className="space-y-3">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                          <div className="flex-1">
                            <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                            <div className="h-2 bg-gray-100 rounded w-1/2 mt-1"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Screenshot 3 */}
                <div className="bg-white rounded-2xl shadow-lg p-6 hover:scale-105 transition-transform duration-500 col-span-2">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-bold text-gray-900">Projekty</h4>
                      <div className="w-6 h-6 bg-purple-600 rounded-full"></div>
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="space-y-2">
                          <div className="h-16 bg-gray-100 rounded-lg"></div>
                          <div className="h-2 bg-gray-200 rounded"></div>
                          <div className="h-2 bg-gray-100 rounded w-2/3"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Badge */}
              <div className="absolute -top-4 -right-4 bg-emerald-600 text-white p-3 rounded-xl shadow-lg animate-pulse">
                <div className="text-sm font-bold">Nové!</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-600 via-emerald-700 to-blue-600 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-white/20 to-transparent"></div>
          <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-24 h-24 bg-white/10 rounded-full animate-bounce"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/10 rounded-full animate-pulse"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 lg:px-8 text-center relative z-10">
          <div className="space-y-8">
            <h2 className="text-5xl lg:text-6xl font-bold text-white leading-tight">
              Vyzkoušejte si to sami, začněte
              <br />
              <span className="text-emerald-200">za 5 minut</span>
            </h2>

            <p className="text-xl text-emerald-100 max-w-3xl mx-auto leading-relaxed">
              Připojte se k tisícům spokojených uživatelů a začněte řídit svůj tým
              efektivně už dnes. Žádné skryté poplatky, žádné závazky.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <button className="bg-white text-emerald-600 px-10 py-5 rounded-2xl font-bold text-lg hover:bg-gray-50 hover:scale-105 transition-all duration-300 flex items-center group shadow-2xl hover:shadow-3xl">
                Začít zdarma
                <span className="ml-3 group-hover:translate-x-1 transition-transform">→</span>
              </button>

              <button className="border-2 border-white text-white px-10 py-5 rounded-2xl font-bold text-lg hover:bg-white hover:text-emerald-600 hover:scale-105 transition-all duration-300 flex items-center group">
                <span className="mr-3">📱</span>
                Stáhnout aplikaci
              </button>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-emerald-200">
              <div className="flex items-center space-x-2">
                <span className="text-lg">✓</span>
                <span>Bezplatná registrace</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-lg">✓</span>
                <span>Bez kreditní karty</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-lg">✓</span>
                <span>Zrušit kdykoli</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-4 gap-8 mb-12">
            {/* Company Info */}
            <div className="lg:col-span-1">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-emerald-600 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">G</span>
                </div>
                <span className="text-2xl font-bold">GrowyNet</span>
              </div>
              <p className="text-gray-400 mb-6 leading-relaxed">
                Moderní platforma pro správu zaměstnanců a projektů,
                která vám pomůže řídit váš tým efektivně a transparentně.
              </p>
              <div className="flex space-x-4">
                {['📘', '🐦', '📷', '💼'].map((icon, index) => (
                  <a
                    key={index}
                    href="#"
                    className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-emerald-600 hover:scale-110 transition-all duration-300"
                  >
                    <span className="text-lg">{icon}</span>
                  </a>
                ))}
              </div>
            </div>

            {/* Products */}
            <div>
              <h4 className="font-bold text-lg mb-6">Produkty</h4>
              <ul className="space-y-3">
                {['Správa týmu', 'Řízení projektů', 'Analýzy', 'Mobilní aplikace'].map((item) => (
                  <li key={item}>
                    <a href="#" className="text-gray-400 hover:text-white transition-colors hover:translate-x-1 transform duration-300 inline-block">
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="font-bold text-lg mb-6">Podpora</h4>
              <ul className="space-y-3">
                {['Nápověda', 'Kontakt', 'Bezpečnost', 'Status stránky'].map((item) => (
                  <li key={item}>
                    <a href="#" className="text-gray-400 hover:text-white transition-colors hover:translate-x-1 transform duration-300 inline-block">
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company */}
            <div>
              <h4 className="font-bold text-lg mb-6">Společnost</h4>
              <ul className="space-y-3">
                {['O nás', 'Kariéra', 'Blog', 'Partneři'].map((item) => (
                  <li key={item}>
                    <a href="#" className="text-gray-400 hover:text-white transition-colors hover:translate-x-1 transform duration-300 inline-block">
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 flex flex-col lg:flex-row justify-between items-center">
            <p className="text-gray-400 mb-4 lg:mb-0">
              &copy; 2024 GrowyNet. Všechna práva vyhrazena.
            </p>
            <div className="flex space-x-6 text-sm">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">Podmínky použití</a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">Ochrana soukromí</a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">Cookies</a>
            </div>
          </div>
        </div>
      </footer>
    </main>
  );
}

          {/* Hero Content */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Vaše finance
                  <br />
                  <span className="text-emerald-600">na dosah</span>
                </h1>

                <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                  Moderní platforma pro správu financí, která vám pomůže řídit vaše peníze chytře a efektivně.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-emerald-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-emerald-700 hover:scale-105 transition-all duration-300 flex items-center justify-center group">
                  Začít zdarma
                  <span className="ml-2 group-hover:translate-x-1 transition-transform">→</span>
                </button>

                <button className="border border-gray-300 text-gray-700 px-8 py-4 rounded-xl font-semibold hover:bg-gray-50 hover:scale-105 transition-all duration-300 flex items-center justify-center group">
                  <span className="mr-2">▶</span>
                  Sledovat demo
                </button>
              </div>

              <div className="flex items-center space-x-6 text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Bezpečné</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Rychlé</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span>Spolehlivé</span>
                </div>
              </div>
            </div>

            {/* Phone Mockup */}
            <div className="relative flex justify-center lg:justify-end">
              <div className="relative w-80 h-[600px] bg-gray-900 rounded-[3rem] p-2 shadow-2xl">
                <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
                  {/* Status Bar */}
                  <div className="flex justify-between items-center px-6 py-3 bg-gray-50">
                    <span className="text-sm font-semibold">9:41</span>
                    <div className="flex items-center space-x-1">
                      <div className="w-4 h-2 bg-gray-300 rounded-sm"></div>
                      <div className="w-6 h-3 border border-gray-300 rounded-sm">
                        <div className="w-4 h-full bg-green-500 rounded-sm"></div>
                      </div>
                    </div>
                  </div>

                  {/* App Interface */}
                  <div className="p-6 space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900">Dobrý den!</h2>
                        <p className="text-gray-600">Zde je váš přehled</p>
                      </div>
                      <div className="w-10 h-10 bg-emerald-600 rounded-full flex items-center justify-center">
                        <span className="text-white">🔔</span>
                      </div>
                    </div>

                    {/* Balance Card */}
                    <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-2xl p-6 text-white animate-pulse">
                      <p className="text-emerald-100 text-sm">Celkový zůstatek</p>
                      <p className="text-3xl font-bold">124,567 Kč</p>
                      <div className="flex items-center mt-2">
                        <span className="mr-1">📈</span>
                        <span className="text-sm">+2.5% tento týden</span>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 rounded-xl p-4 text-center hover:scale-105 transition-transform cursor-pointer">
                        <div className="w-8 h-8 bg-blue-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                          <span className="text-white text-sm">💳</span>
                        </div>
                        <p className="text-sm font-medium">Převést</p>
                      </div>
                      <div className="bg-gray-50 rounded-xl p-4 text-center hover:scale-105 transition-transform cursor-pointer">
                        <div className="w-8 h-8 bg-purple-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                          <span className="text-white text-sm">📈</span>
                        </div>
                        <p className="text-sm font-medium">Investovat</p>
                      </div>
                    </div>
                  </div>

                  {/* Dynamic Island */}
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-gray-900 rounded-full"></div>
                </div>
              </div>

              {/* Floating Notification */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-72 bg-white rounded-2xl shadow-2xl border border-gray-100 p-4 z-10 animate-bounce">
                <div className="flex items-start space-x-3">
                  <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white flex-shrink-0">
                    <span className="text-lg">💳</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-gray-900 truncate">
                      Platba přijata
                    </p>
                    <p className="text-sm text-gray-600 mt-1">
                      Obdrželi jste 2,450 Kč
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      právě teď
                    </p>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -right-8 top-20 w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center shadow-lg animate-pulse">
                <span className="text-2xl text-emerald-600">💳</span>
              </div>

              <div className="absolute -left-8 bottom-32 w-14 h-14 bg-blue-100 rounded-xl flex items-center justify-center shadow-lg animate-bounce">
                <span className="text-xl text-blue-600">📈</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Měříme výkon pro vás
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Naše platforma přináší měřitelné výsledky, které pomohou vašemu podnikání růst rychleji a efektivněji.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              { value: '30%', label: 'Rychlejší transakce', description: 'Oproti tradičním bankám' },
              { value: '93%', label: 'Spokojenost klientů', description: 'Na základě hodnocení' },
              { value: '55%', label: 'Úspora času', description: 'Při správě financí' }
            ].map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
                  <div className="text-5xl font-bold text-emerald-600 mb-4">
                    {stat.value}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {stat.label}
                  </h3>
                  <p className="text-gray-600">
                    {stat.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Digitální transformace,
              <br />
              <span className="text-emerald-600">pro vaše potřeby</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Objevte sílu moderních finančních nástrojů, které vám pomohou řídit peníze chytře a efektivně.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { icon: '🛡️', title: 'Maximální bezpečnost', description: 'Pokročilé šifrování a vícefaktorová autentizace chrání vaše data 24/7.', color: 'bg-green-500' },
              { icon: '⚡', title: 'Bleskové převody', description: 'Převádějte peníze okamžitě mezi účty s nejnižšími poplatky na trhu.', color: 'bg-yellow-500' },
              { icon: '📈', title: 'Chytré investice', description: 'AI-powered doporučení pro optimalizaci vašeho investičního portfolia.', color: 'bg-blue-500' },
              { icon: '💳', title: 'Virtuální karty', description: 'Vytvářejte neomezené množství virtuálních karet pro bezpečné online nákupy.', color: 'bg-purple-500' },
              { icon: '👥', title: 'Týmová spolupráce', description: 'Spravujte firemní finance s pokročilými nástroji pro týmovou spolupráci.', color: 'bg-pink-500' },
              { icon: '📊', title: 'Detailní analýzy', description: 'Získejte přehled o svých výdajích s pokročilými analytickými nástroji.', color: 'bg-indigo-500' }
            ].map((feature, index) => (
              <div key={index} className="group hover:-translate-y-2 transition-transform duration-300">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 h-full">
                  <div className={`w-16 h-16 ${feature.color} rounded-2xl flex items-center justify-center text-white mb-6 group-hover:shadow-lg transition-shadow hover:scale-110 hover:rotate-3`}>
                    <span className="text-2xl">{feature.icon}</span>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors">
                    {feature.title}
                  </h3>

                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>

                  <div className="h-1 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full mt-6" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-emerald-600">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 text-center">
          <h2 className="text-5xl font-bold text-white mb-6">
            Vyzkoušejte si to sami, začněte
            <br />
            <span className="text-emerald-200">za 5 minut</span>
          </h2>
          <p className="text-xl text-emerald-100 max-w-3xl mx-auto mb-12">
            Připojte se k tisícům spokojených uživatelů a začněte řídit své finance chytře už dnes.
            Žádné skryté poplatky, žádné závazky.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <button className="bg-white text-emerald-600 px-10 py-5 rounded-2xl font-bold text-lg hover:bg-gray-50 hover:scale-105 transition-all duration-300 flex items-center group shadow-lg hover:shadow-xl">
              Začít zdarma
              <span className="ml-3 group-hover:translate-x-1 transition-transform">→</span>
            </button>

            <button className="border-2 border-white text-white px-10 py-5 rounded-2xl font-bold text-lg hover:bg-white hover:text-emerald-600 hover:scale-105 transition-all duration-300 flex items-center group">
              <span className="mr-3">📱</span>
              Stáhnout aplikaci
            </button>
          </div>

          <p className="text-sm text-emerald-200 mt-6">
            Bezplatná registrace • Bez kreditní karty • Zrušit kdykoli
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="grid lg:grid-cols-4 gap-8">
            <div className="lg:col-span-1">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-emerald-600 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">F</span>
                </div>
                <span className="text-2xl font-bold">FinanceApp</span>
              </div>
              <p className="text-gray-400 mb-6 leading-relaxed">
                Moderní platforma pro správu financí, která vám pomůže řídit
                vaše peníze chytře a efektivně.
              </p>
            </div>

            <div>
              <h4 className="font-bold text-lg mb-6">Produkty</h4>
              <ul className="space-y-3">
                {['Osobní finance', 'Firemní účty', 'Investice', 'Kreditní karty'].map((item) => (
                  <li key={item}>
                    <a href="#" className="text-gray-400 hover:text-white transition-colors">
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="font-bold text-lg mb-6">Podpora</h4>
              <ul className="space-y-3">
                {['Nápověda', 'Kontakt', 'Bezpečnost', 'Status stránky'].map((item) => (
                  <li key={item}>
                    <a href="#" className="text-gray-400 hover:text-white transition-colors">
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="font-bold text-lg mb-6">Společnost</h4>
              <ul className="space-y-3">
                {['O nás', 'Kariéra', 'Blog', 'Partneři'].map((item) => (
                  <li key={item}>
                    <a href="#" className="text-gray-400 hover:text-white transition-colors">
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col lg:flex-row justify-between items-center">
            <p className="text-gray-400">&copy; 2024 FinanceApp. Všechna práva vyhrazena.</p>
            <div className="flex space-x-4 mt-4 lg:mt-0">
              {['📘', '🐦', '📷', '💼'].map((icon, index) => (
                <a
                  key={index}
                  href="#"
                  className="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center hover:bg-emerald-600 hover:scale-110 transition-all duration-300"
                >
                  <span className="text-xl">{icon}</span>
                </a>
              ))}
            </div>
          </div>
        </div>
      </footer>
    </main>
  );
}
