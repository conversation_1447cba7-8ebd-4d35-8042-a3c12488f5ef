'use client';

interface Feature {
  icon: string;
  title: string;
  description: string;
  color: string;
}

const features: Feature[] = [
  {
    icon: '🛡️',
    title: 'Maximální bezpečnost',
    description: 'Pokročilé šifrování a vícefaktorová autentizace chrání vaše data 24/7.',
    color: 'bg-green-500'
  },
  {
    icon: '⚡',
    title: 'Bleskové převody',
    description: 'Převádějte peníze okamžitě mezi účty s nejnižšími poplatky na trhu.',
    color: 'bg-yellow-500'
  },
  {
    icon: '📈',
    title: 'Chytré investice',
    description: 'AI-powered doporučení pro optimalizaci vašeho investičního portfolia.',
    color: 'bg-blue-500'
  },
  {
    icon: '💳',
    title: 'Virtuální karty',
    description: 'Vytvářejte neomezené množství virtuálních karet pro bezpečné online nákupy.',
    color: 'bg-purple-500'
  },
  {
    icon: '👥',
    title: '<PERSON>ýmová spolupráce',
    description: 'Spravujte firemní finance s pokročilými nástroji pro týmovou spolupráci.',
    color: 'bg-pink-500'
  },
  {
    icon: '📊',
    title: 'Detailní analýzy',
    description: 'Získejte přehled o svých výdajích s pokročilými analytickými nástroji.',
    color: 'bg-indigo-500'
  }
];

export default function FeaturesSection() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Digitální transformace,
            <br />
            <span className="text-emerald-600">pro vaše potřeby</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Objevte sílu moderních finančních nástrojů, které vám pomohou řídit peníze chytře a efektivně.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="group hover:-translate-y-2 transition-transform duration-300">
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 h-full">
                <div className={`w-16 h-16 ${feature.color} rounded-2xl flex items-center justify-center text-white mb-6 group-hover:shadow-lg transition-shadow hover:scale-110 hover:rotate-3`}>
                  <span className="text-2xl">{feature.icon}</span>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors">
                  {feature.title}
                </h3>

                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>

                <div className="h-1 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full mt-6" />
              </div>
            </div>
          ))}
        </div>

        {/* Additional Feature Showcase */}
        <div className="mt-20">
          <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-3xl p-12 text-white relative overflow-hidden">
            <div className="relative z-10 grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-bold mb-6">
                  Připraveni na budoucnost financí?
                </h3>
                <p className="text-emerald-100 text-lg mb-8 leading-relaxed">
                  Naše platforma využívá nejnovější technologie včetně umělé inteligence,
                  blockchain zabezpečení a real-time analytics pro poskytnutí nejlepšího
                  finančního zážitku.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <button className="bg-white text-emerald-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-50 hover:scale-105 transition-all">
                    Začít zdarma
                  </button>
                  <button className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-emerald-600 hover:scale-105 transition-all">
                    Kontaktovat prodej
                  </button>
                </div>
              </div>
              
              <div className="relative">
                <div className="w-64 h-64 mx-auto relative animate-spin-slow">
                  {/* Outer ring */}
                  <div className="absolute inset-0 border-4 border-emerald-300 rounded-full opacity-30"></div>

                  {/* Middle ring */}
                  <div className="absolute inset-8 border-4 border-emerald-200 rounded-full opacity-50"></div>

                  {/* Inner circle */}
                  <div className="absolute inset-16 bg-white rounded-full flex items-center justify-center">
                    <div className="text-emerald-600 text-4xl font-bold">AI</div>
                  </div>

                  {/* Floating elements */}
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-white rounded-full flex items-center justify-center animate-bounce">
                    <span className="text-emerald-600">🛡️</span>
                  </div>

                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-white rounded-full flex items-center justify-center animate-pulse">
                    <span className="text-emerald-600">⚡</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-emerald-400 rounded-full opacity-10 -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-emerald-400 rounded-full opacity-10 translate-y-24 -translate-x-24"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
