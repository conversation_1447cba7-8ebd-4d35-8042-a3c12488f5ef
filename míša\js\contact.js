// Contact Page Specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Contact form handling
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const data = {
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                email: formData.get('email'),
                subject: formData.get('subject'),
                message: formData.get('message'),
                newsletter: formData.get('newsletter') === 'on',
                privacy: formData.get('privacy') === 'on'
            };
            
            // Validate required fields
            if (!data.firstName || !data.lastName || !data.email || !data.message || !data.privacy) {
                showNotification('Prosím vyplňte všechna povinná pole.', 'error');
                return;
            }
            
            // Validate email
            if (!isValidEmail(data.email)) {
                showNotification('Prosím zadejte platnou emailovou adresu.', 'error');
                return;
            }
            
            // Submit form
            submitContactForm(data);
        });
    }

    // FAQ accordion functionality
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');
            
            // Close all other FAQ items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });
            
            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
            } else {
                item.classList.add('active');
            }
        });
    });

    // Service cards hover effects
    const serviceCards = document.querySelectorAll('.service-card');
    
    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.service-icon');
            icon.style.transform = 'scale(1.1) rotate(5deg)';
            icon.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.service-icon');
            icon.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // Contact items animation on scroll
    const contactItems = document.querySelectorAll('.contact-item');
    
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const contactObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 200);
            }
        });
    }, observerOptions);

    contactItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        contactObserver.observe(item);
    });

    // Form validation helpers
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Submit contact form
    function submitContactForm(data) {
        const submitButton = contactForm.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        
        // Show loading state
        submitButton.textContent = 'Odesílám...';
        submitButton.disabled = true;
        
        // Simulate form submission (in real app, this would be an API call)
        setTimeout(() => {
            // Success simulation
            submitButton.textContent = 'Odesláno!';
            submitButton.style.background = '#4CAF50';
            
            showNotification('Děkuji za vaši zprávu! Odpovím vám do 24 hodin.', 'success');
            
            // Reset form
            setTimeout(() => {
                contactForm.reset();
                submitButton.textContent = originalText;
                submitButton.disabled = false;
                submitButton.style.background = '';
            }, 2000);
            
            // Track form submission
            trackEvent('contact_form_submit', 'contact', data.subject || 'general');
            
        }, 1500);
    }

    // Show notification function
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            info: 'var(--accent-rose)'
        };
        
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: ${colors[type]};
            color: var(--soft-white);
            padding: 1rem 1.5rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px var(--soft-shadow);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            font-size: 0.9rem;
            line-height: 1.4;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 4000);
    }

    // Social links tracking
    const socialLinks = document.querySelectorAll('.contact-social .social-link');
    socialLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const platform = this.querySelector('i').className.split('-')[1];
            trackEvent('social_click', 'contact', platform);
        });
    });

    // Form field focus effects
    const formInputs = document.querySelectorAll('.contact-form input, .contact-form select, .contact-form textarea');
    
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            if (this.value) {
                this.parentElement.classList.add('filled');
            } else {
                this.parentElement.classList.remove('filled');
            }
        });
        
        // Check if field is pre-filled
        if (input.value) {
            input.parentElement.classList.add('filled');
        }
    });

    // Auto-resize textarea
    const textarea = document.querySelector('#message');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    }

    // Subject selection helper
    const subjectSelect = document.querySelector('#subject');
    const messageTextarea = document.querySelector('#message');
    
    if (subjectSelect && messageTextarea) {
        subjectSelect.addEventListener('change', function() {
            const subject = this.value;
            let placeholder = 'Napište mi, jak vám mohu pomoci...';
            
            switch(subject) {
                case 'styling':
                    placeholder = 'Popište mi váš styl, co hledáte a jaké máte představy o styling konzultaci...';
                    break;
                case 'collaboration':
                    placeholder = 'Představte mi svou značku a typ spolupráce, kterou máte na mysli...';
                    break;
                case 'press':
                    placeholder = 'Uveďte prosím médium, které zastupujete a téma vašeho dotazu...';
                    break;
                case 'feedback':
                    placeholder = 'Vaše zpětná vazba je pro mě velmi cenná. Podělte se o své myšlenky...';
                    break;
            }
            
            messageTextarea.placeholder = placeholder;
        });
    }

    // Copy email to clipboard
    const emailElements = document.querySelectorAll('.contact-details p');
    emailElements.forEach(element => {
        if (element.textContent.includes('@')) {
            element.style.cursor = 'pointer';
            element.title = 'Klikněte pro zkopírování emailu';
            
            element.addEventListener('click', function() {
                const email = this.textContent.trim();
                navigator.clipboard.writeText(email).then(() => {
                    showNotification(`Email ${email} zkopírován do schránky!`, 'success');
                });
            });
        }
    });

    // Analytics tracking function
    function trackEvent(action, category, label) {
        console.log(`Analytics: ${action} - ${category} - ${label}`);
        // In real app: gtag('event', action, { category, label });
    }

    // Track page engagement
    let engagementStartTime = Date.now();
    let maxScrollDepth = 0;
    
    window.addEventListener('scroll', () => {
        const scrollDepth = (window.pageYOffset / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
        maxScrollDepth = Math.max(maxScrollDepth, scrollDepth);
    });
    
    window.addEventListener('beforeunload', () => {
        const engagementTime = (Date.now() - engagementStartTime) / 1000;
        trackEvent('page_engagement', 'contact', `${engagementTime}s_${maxScrollDepth.toFixed(1)}%`);
    });

    // Add floating contact button for mobile
    function addFloatingContactButton() {
        if (window.innerWidth <= 768) {
            const floatingBtn = document.createElement('div');
            floatingBtn.className = 'floating-contact-btn';
            floatingBtn.innerHTML = '<i class="fas fa-envelope"></i>';
            floatingBtn.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 60px;
                height: 60px;
                background: var(--accent-rose);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: var(--soft-white);
                font-size: 1.5rem;
                box-shadow: 0 5px 20px rgba(212, 165, 116, 0.4);
                cursor: pointer;
                z-index: 1000;
                transition: all 0.3s ease;
            `;
            
            floatingBtn.addEventListener('click', () => {
                document.querySelector('.contact-form').scrollIntoView({
                    behavior: 'smooth'
                });
            });
            
            document.body.appendChild(floatingBtn);
        }
    }

    addFloatingContactButton();

    // Console message
    console.log('%c📧 Kontaktní stránka načtena! Těším se na vaši zprávu 📧', 'color: #d4a574; font-size: 14px; font-weight: bold;');
});
