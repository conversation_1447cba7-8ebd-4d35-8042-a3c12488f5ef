<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NEXUS PHONE - Futuristické Notifikace</title>
    <style>
        :root {
            --neon-cyan: #00ffff;
            --neon-purple: #ff00ff;
            --neon-blue: #0080ff;
            --dark-bg: #0a0a0f;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --hologram-blue: #4dd0e1;
            --quantum-pink: #e91e63;
            --neural-green: #00e676;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            min-height: 100vh;
            background:
                radial-gradient(circle at 20% 20%, var(--neon-cyan) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, var(--neon-purple) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, var(--neon-blue) 0%, transparent 40%),
                linear-gradient(135deg, var(--dark-bg) 0%, #1a1a2e 50%, #16213e 100%);
            background-size: 300% 300%, 400% 400%, 250% 250%, 100% 100%;
            animation:
                neuralNetworkPulse 12s ease-in-out infinite,
                cosmicDrift 25s linear infinite,
                quantumFluctuation 8s ease-in-out infinite alternate;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            overflow: hidden;
            position: relative;
            perspective: 2000px;
        }

        @keyframes neuralNetworkPulse {
            0%, 100% {
                background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%;
                filter: brightness(1) contrast(1.2) saturate(1.5);
            }
            33% {
                background-position: 100% 50%, 50% 100%, 75% 25%, 0% 0%;
                filter: brightness(1.1) contrast(1.3) saturate(1.7);
            }
            66% {
                background-position: 200% 100%, 100% 0%, 150% 75%, 0% 0%;
                filter: brightness(0.9) contrast(1.1) saturate(1.3);
            }
        }

        @keyframes cosmicDrift {
            0% { background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%; }
            100% { background-position: 300% 300%, 400% 400%, 250% 250%, 0% 0%; }
        }

        @keyframes quantumFluctuation {
            0% {
                transform: scale(1) rotate(0deg);
                filter: hue-rotate(0deg) brightness(1);
            }
            100% {
                transform: scale(1.02) rotate(0.5deg);
                filter: hue-rotate(30deg) brightness(1.1);
            }
        }

        /* Futuristický neural particle systém */
        .neural-particle {
            position: absolute;
            pointer-events: none;
            border-radius: 50%;
            background:
                radial-gradient(circle at 30% 30%, var(--neon-cyan) 0%, transparent 40%),
                radial-gradient(circle at 70% 70%, var(--neon-purple) 0%, transparent 40%),
                conic-gradient(from 0deg, var(--hologram-blue) 0deg, var(--quantum-pink) 120deg, var(--neural-green) 240deg, var(--hologram-blue) 360deg);
            filter: blur(1px);
            animation:
                neuralFloat 15s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite,
                neuralGlow 4s ease-in-out infinite alternate,
                neuralSpin 8s linear infinite;
            box-shadow:
                0 0 20px var(--neon-cyan),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
        }

        @keyframes neuralFloat {
            0%, 100% {
                transform: translateY(110vh) translateX(0px) translateZ(0px) rotate(0deg) scale(0.5);
                opacity: 0;
                filter: blur(3px) hue-rotate(0deg);
            }
            10% {
                opacity: 0.7;
                transform: translateY(80vh) translateX(20px) translateZ(10px) rotate(60deg) scale(0.8);
                filter: blur(2px) hue-rotate(60deg);
            }
            50% {
                opacity: 1;
                transform: translateY(40vh) translateX(-30px) translateZ(-15px) rotate(180deg) scale(1.2);
                filter: blur(1px) hue-rotate(180deg);
            }
            90% {
                opacity: 0.8;
                transform: translateY(5vh) translateX(40px) translateZ(20px) rotate(300deg) scale(0.9);
                filter: blur(2px) hue-rotate(300deg);
            }
        }

        @keyframes neuralGlow {
            0% {
                box-shadow:
                    0 0 20px var(--neon-cyan),
                    inset 0 0 20px rgba(255, 255, 255, 0.1);
            }
            100% {
                box-shadow:
                    0 0 40px var(--neon-purple),
                    0 0 60px var(--hologram-blue),
                    inset 0 0 30px rgba(255, 255, 255, 0.2);
            }
        }

        @keyframes neuralSpin {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.1); }
            50% { transform: rotate(180deg) scale(0.9); }
            75% { transform: rotate(270deg) scale(1.05); }
            100% { transform: rotate(360deg) scale(1); }
        }

        /* Futuristický holografický phone container */
        .nexus-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            transform-style: preserve-3d;
            animation:
                nexusOrbit 18s cubic-bezier(0.23, 1, 0.32, 1) infinite,
                hologramLevitation 10s ease-in-out infinite alternate,
                quantumField 7s ease-in-out infinite alternate;
        }

        .nexus-container::before {
            content: '';
            position: absolute;
            top: -50px;
            left: -50px;
            right: -50px;
            bottom: -50px;
            background:
                conic-gradient(from 0deg,
                    transparent 0deg,
                    var(--neon-cyan) 45deg,
                    transparent 90deg,
                    var(--neon-purple) 135deg,
                    transparent 180deg,
                    var(--neon-blue) 225deg,
                    transparent 270deg,
                    var(--neural-green) 315deg,
                    transparent 360deg);
            border-radius: 50%;
            filter: blur(20px);
            opacity: 0.4;
            animation: hologramRing 12s linear infinite;
            z-index: -1;
        }

        @keyframes nexusOrbit {
            0% {
                transform: translateY(0px) translateZ(0px) rotateY(0deg) rotateX(0deg) rotateZ(0deg);
            }
            20% {
                transform: translateY(-25px) translateZ(40px) rotateY(72deg) rotateX(8deg) rotateZ(3deg);
            }
            40% {
                transform: translateY(-40px) translateZ(20px) rotateY(144deg) rotateX(-5deg) rotateZ(-2deg);
            }
            60% {
                transform: translateY(-30px) translateZ(-30px) rotateY(216deg) rotateX(10deg) rotateZ(4deg);
            }
            80% {
                transform: translateY(-10px) translateZ(15px) rotateY(288deg) rotateX(-3deg) rotateZ(-1deg);
            }
            100% {
                transform: translateY(0px) translateZ(0px) rotateY(360deg) rotateX(0deg) rotateZ(0deg);
            }
        }

        @keyframes hologramLevitation {
            0% {
                transform: translateY(0px) scale(1);
                filter: brightness(1) contrast(1.2);
            }
            100% {
                transform: translateY(-50px) scale(1.03);
                filter: brightness(1.2) contrast(1.4);
            }
        }

        @keyframes quantumField {
            0% {
                filter:
                    drop-shadow(0 40px 80px rgba(0, 0, 0, 0.6))
                    drop-shadow(0 0 60px var(--neon-cyan))
                    drop-shadow(0 0 100px var(--hologram-blue));
            }
            100% {
                filter:
                    drop-shadow(0 60px 120px rgba(0, 0, 0, 0.8))
                    drop-shadow(0 0 100px var(--neon-purple))
                    drop-shadow(0 0 150px var(--quantum-pink))
                    drop-shadow(0 0 200px var(--neural-green));
            }
        }

        @keyframes hologramRing {
            0% { transform: rotate(0deg) scale(1); opacity: 0.4; }
            50% { transform: rotate(180deg) scale(1.1); opacity: 0.6; }
            100% { transform: rotate(360deg) scale(1); opacity: 0.4; }
        }

        /* Nexus Phone - Futuristický design */
        .nexus-phone {
            width: 380px;
            height: auto;
            cursor: pointer;
            transform-style: preserve-3d;
            transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
            z-index: 10;
            position: relative;
            animation:
                nexusQuantumPulse 5s ease-in-out infinite,
                nexusHologramShift 15s linear infinite,
                nexusEnergyFlow 8s ease-in-out infinite alternate;
            border-radius: 30px;
            overflow: hidden;
        }

        .nexus-phone::before {
            content: '';
            position: absolute;
            top: -30px;
            left: -30px;
            right: -30px;
            bottom: -30px;
            background:
                conic-gradient(from 0deg,
                    transparent 0deg,
                    var(--neon-cyan) 60deg,
                    transparent 120deg,
                    var(--neon-purple) 180deg,
                    transparent 240deg,
                    var(--neural-green) 300deg,
                    transparent 360deg);
            border-radius: 60px;
            filter: blur(25px);
            opacity: 0.5;
            animation: nexusAuraRotation 10s linear infinite;
            z-index: -1;
        }

        .nexus-phone::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(45deg,
                    rgba(0, 255, 255, 0.1) 0%,
                    transparent 25%,
                    rgba(255, 0, 255, 0.1) 50%,
                    transparent 75%,
                    rgba(0, 230, 118, 0.1) 100%);
            border-radius: 30px;
            animation: nexusGlassEffect 6s ease-in-out infinite alternate;
            z-index: 1;
            pointer-events: none;
        }

        @keyframes nexusQuantumPulse {
            0%, 100% {
                transform: scale(1) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
                filter: brightness(1) contrast(1.3) saturate(1.5);
            }
            25% {
                transform: scale(1.008) rotateX(2deg) rotateY(3deg) rotateZ(1deg);
                filter: brightness(1.05) contrast(1.35) saturate(1.6);
            }
            50% {
                transform: scale(1.015) rotateX(0deg) rotateY(0deg) rotateZ(-1deg);
                filter: brightness(1.1) contrast(1.4) saturate(1.7);
            }
            75% {
                transform: scale(1.008) rotateX(-2deg) rotateY(-3deg) rotateZ(1deg);
                filter: brightness(1.05) contrast(1.35) saturate(1.6);
            }
        }

        @keyframes nexusHologramShift {
            0%, 100% {
                box-shadow:
                    0 0 60px var(--neon-cyan),
                    inset 0 0 40px rgba(0, 255, 255, 0.2),
                    0 0 100px var(--hologram-blue);
            }
            33% {
                box-shadow:
                    0 0 80px var(--neon-purple),
                    inset 0 0 50px rgba(255, 0, 255, 0.2),
                    0 0 120px var(--quantum-pink);
            }
            66% {
                box-shadow:
                    0 0 70px var(--neural-green),
                    inset 0 0 45px rgba(0, 230, 118, 0.2),
                    0 0 110px var(--neon-blue);
            }
        }

        @keyframes nexusEnergyFlow {
            0% {
                filter: hue-rotate(0deg) brightness(1) contrast(1.3);
            }
            100% {
                filter: hue-rotate(60deg) brightness(1.2) contrast(1.5);
            }
        }

        @keyframes nexusAuraRotation {
            0% { transform: rotate(0deg) scale(1); opacity: 0.5; }
            25% { transform: rotate(90deg) scale(1.05); opacity: 0.7; }
            50% { transform: rotate(180deg) scale(1.1); opacity: 0.6; }
            75% { transform: rotate(270deg) scale(1.05); opacity: 0.8; }
            100% { transform: rotate(360deg) scale(1); opacity: 0.5; }
        }

        @keyframes nexusGlassEffect {
            0% {
                opacity: 0.3;
                background: linear-gradient(45deg,
                    rgba(0, 255, 255, 0.1) 0%,
                    transparent 50%,
                    rgba(255, 0, 255, 0.1) 100%);
            }
            100% {
                opacity: 0.6;
                background: linear-gradient(45deg,
                    rgba(255, 0, 255, 0.15) 0%,
                    transparent 50%,
                    rgba(0, 230, 118, 0.15) 100%);
            }
        }

        .nexus-phone:hover {
            transform: scale(1.12) rotateX(-10deg) rotateY(15deg) translateZ(60px);
            filter:
                brightness(1.3) contrast(1.6) saturate(2) hue-rotate(30deg)
                drop-shadow(0 80px 160px rgba(0, 0, 0, 0.7))
                drop-shadow(0 0 120px var(--neon-cyan))
                drop-shadow(0 0 180px var(--neon-purple));
            animation-play-state: paused;
        }

        .nexus-phone:active {
            transform: scale(0.88) rotateX(12deg) rotateY(-12deg) translateZ(-30px);
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Futuristické neural notifikace */
        .neural-notification {
            position: absolute;
            top: 4%;
            left: 55%;
            transform: translateX(-50%) translateY(-200px) translateZ(0px);
            width: 320px;
            height: auto;
            opacity: 0;
            z-index: 20;
            pointer-events: auto;
            border-radius: 25px;
            overflow: hidden;
            transform-style: preserve-3d;
            backface-visibility: hidden;
            cursor: pointer;
            transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .neural-notification::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background:
                conic-gradient(from 0deg,
                    var(--neon-cyan) 0deg,
                    transparent 60deg,
                    var(--neon-purple) 120deg,
                    transparent 180deg,
                    var(--neural-green) 240deg,
                    transparent 300deg,
                    var(--neon-cyan) 360deg);
            border-radius: 28px;
            filter: blur(12px);
            opacity: 0;
            animation: neuralBorderPulse 4s ease-in-out infinite alternate;
            z-index: -1;
        }

        .neural-notification::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(135deg,
                    rgba(0, 255, 255, 0.08) 0%,
                    transparent 30%,
                    rgba(255, 0, 255, 0.08) 50%,
                    transparent 70%,
                    rgba(0, 230, 118, 0.08) 100%);
            border-radius: 25px;
            animation: neuralGlassShimmer 8s ease-in-out infinite;
            z-index: 1;
            pointer-events: none;
        }

        @keyframes neuralBorderPulse {
            0% {
                opacity: 0.4;
                transform: scale(1) rotate(0deg);
            }
            100% {
                opacity: 0.8;
                transform: scale(1.03) rotate(180deg);
            }
        }

        @keyframes neuralGlassShimmer {
            0%, 100% {
                background: linear-gradient(135deg,
                    rgba(0, 255, 255, 0.08) 0%,
                    transparent 50%,
                    rgba(255, 0, 255, 0.08) 100%);
            }
            33% {
                background: linear-gradient(135deg,
                    rgba(255, 0, 255, 0.12) 0%,
                    transparent 50%,
                    rgba(0, 230, 118, 0.12) 100%);
            }
            66% {
                background: linear-gradient(135deg,
                    rgba(0, 230, 118, 0.10) 0%,
                    transparent 50%,
                    rgba(0, 255, 255, 0.10) 100%);
            }
        }

        .neural-notification:nth-child(1) {
            animation:
                neuralQuantumMaterialize 20s cubic-bezier(0.23, 1, 0.32, 1) infinite,
                neuralEnergyPulse 5s ease-in-out infinite alternate,
                neuralDimensionalFloat 12s ease-in-out infinite;
            animation-delay: 0s, 0.8s, 1.5s;
        }

        .neural-notification:nth-child(2) {
            animation:
                neuralQuantumMaterialize 20s cubic-bezier(0.23, 1, 0.32, 1) infinite,
                neuralEnergyPulse 5s ease-in-out infinite alternate,
                neuralDimensionalFloat 12s ease-in-out infinite;
            animation-delay: 3.33s, 4.13s, 4.83s;
        }

        .neural-notification:nth-child(3) {
            animation:
                neuralQuantumMaterialize 20s cubic-bezier(0.23, 1, 0.32, 1) infinite,
                neuralEnergyPulse 5s ease-in-out infinite alternate,
                neuralDimensionalFloat 12s ease-in-out infinite;
            animation-delay: 6.67s, 7.47s, 8.17s;
        }

        .neural-notification:nth-child(4) {
            animation:
                neuralQuantumMaterialize 20s cubic-bezier(0.23, 1, 0.32, 1) infinite,
                neuralEnergyPulse 5s ease-in-out infinite alternate,
                neuralDimensionalFloat 12s ease-in-out infinite;
            animation-delay: 10s, 10.8s, 11.5s;
        }

        .neural-notification:nth-child(5) {
            animation:
                neuralQuantumMaterialize 20s cubic-bezier(0.23, 1, 0.32, 1) infinite,
                neuralEnergyPulse 5s ease-in-out infinite alternate,
                neuralDimensionalFloat 12s ease-in-out infinite;
            animation-delay: 13.33s, 14.13s, 14.83s;
        }

        .neural-notification:nth-child(6) {
            animation:
                neuralQuantumMaterialize 20s cubic-bezier(0.23, 1, 0.32, 1) infinite,
                neuralEnergyPulse 5s ease-in-out infinite alternate,
                neuralDimensionalFloat 12s ease-in-out infinite;
            animation-delay: 16.67s, 17.47s, 18.17s;
        }



        /* Futuristické neural keyframe animace */
        @keyframes neuralQuantumMaterialize {
            0% {
                opacity: 0;
                transform: translateX(-50%) translateY(-250px) translateZ(-150px)
                          rotateX(-150deg) rotateY(30deg) rotateZ(20deg) scale(0.3);
                filter: brightness(0.2) blur(20px) saturate(0.3) hue-rotate(0deg) contrast(0.5)
                        drop-shadow(0 0 0px transparent);
            }
            3% {
                opacity: 0.2;
                transform: translateX(-50%) translateY(-180px) translateZ(-90px)
                          rotateX(-120deg) rotateY(25deg) rotateZ(15deg) scale(0.5);
                filter: brightness(0.4) blur(15px) saturate(0.5) hue-rotate(60deg) contrast(0.7)
                        drop-shadow(0 8px 20px rgba(0, 255, 255, 0.2));
            }
            7% {
                opacity: 0.5;
                transform: translateX(-50%) translateY(-120px) translateZ(-50px)
                          rotateX(-80deg) rotateY(18deg) rotateZ(10deg) scale(0.7);
                filter: brightness(0.6) blur(10px) saturate(0.8) hue-rotate(120deg) contrast(0.9)
                        drop-shadow(0 15px 35px rgba(255, 0, 255, 0.3));
            }
            12% {
                opacity: 0.8;
                transform: translateX(-50%) translateY(-60px) translateZ(-20px)
                          rotateX(-40deg) rotateY(10deg) rotateZ(5deg) scale(0.85);
                filter: brightness(0.8) blur(5px) saturate(1.1) hue-rotate(180deg) contrast(1.1)
                        drop-shadow(0 20px 45px rgba(0, 230, 118, 0.4));
            }
            18% {
                opacity: 1;
                transform: translateX(-50%) translateY(0px) translateZ(0px)
                          rotateX(0deg) rotateY(0deg) rotateZ(0deg) scale(1);
                filter: brightness(1.4) blur(0px) saturate(1.6) hue-rotate(240deg) contrast(1.4)
                        drop-shadow(0 30px 70px rgba(0, 0, 0, 0.4))
                        drop-shadow(0 0 50px var(--neon-cyan))
                        drop-shadow(0 0 80px var(--neon-purple))
                        drop-shadow(0 0 100px var(--neural-green));
            }
            22% {
                transform: translateX(-50%) translateY(-15px) translateZ(12px)
                          rotateX(8deg) rotateY(-5deg) rotateZ(-2deg) scale(1.05);
                filter: brightness(1.6) blur(0px) saturate(1.8) hue-rotate(280deg) contrast(1.5)
                        drop-shadow(0 35px 80px rgba(0, 0, 0, 0.5))
                        drop-shadow(0 0 60px var(--neon-purple))
                        drop-shadow(0 0 90px var(--hologram-blue));
            }
            26% {
                transform: translateX(-50%) translateY(0px) translateZ(0px)
                          rotateX(0deg) rotateY(0deg) rotateZ(0deg) scale(1);
            }
            35% {
                opacity: 1;
                transform: translateX(-50%) translateY(0px) translateZ(0px)
                          rotateX(0deg) rotateY(0deg) rotateZ(0deg) scale(1);
                filter: brightness(1.4) blur(0px) saturate(1.6) hue-rotate(320deg) contrast(1.4)
                        drop-shadow(0 30px 70px rgba(0, 0, 0, 0.4))
                        drop-shadow(0 0 50px var(--neural-green))
                        drop-shadow(0 0 80px var(--quantum-pink));
            }
            40% {
                opacity: 0.8;
                transform: translateX(-50%) translateY(-25px) translateZ(-8px)
                          rotateX(20deg) rotateY(-12deg) rotateZ(6deg) scale(0.95);
                filter: brightness(1.2) blur(3px) saturate(1.3) hue-rotate(360deg) contrast(1.2)
                        drop-shadow(0 20px 45px rgba(0, 0, 0, 0.3));
            }
            45% {
                opacity: 0.5;
                transform: translateX(-50%) translateY(-100px) translateZ(-60px)
                          rotateX(60deg) rotateY(-20deg) rotateZ(12deg) scale(0.75);
                filter: brightness(0.8) blur(8px) saturate(1.0) hue-rotate(400deg) contrast(1.0)
                        drop-shadow(0 15px 35px rgba(0, 0, 0, 0.2));
            }
            50% {
                opacity: 0.2;
                transform: translateX(-50%) translateY(-170px) translateZ(-110px)
                          rotateX(100deg) rotateY(-28deg) rotateZ(18deg) scale(0.5);
                filter: brightness(0.5) blur(15px) saturate(0.7) hue-rotate(440deg) contrast(0.8)
                        drop-shadow(0 8px 20px rgba(0, 0, 0, 0.1));
            }
            55% {
                opacity: 0;
                transform: translateX(-50%) translateY(-250px) translateZ(-150px)
                          rotateX(150deg) rotateY(-35deg) rotateZ(25deg) scale(0.3);
                filter: brightness(0.2) blur(20px) saturate(0.3) hue-rotate(480deg) contrast(0.5)
                        drop-shadow(0 0 0px transparent);
            }
            100% {
                opacity: 0;
                transform: translateX(-50%) translateY(-250px) translateZ(-150px)
                          rotateX(150deg) rotateY(-35deg) rotateZ(25deg) scale(0.3);
                filter: brightness(0.2) blur(20px) saturate(0.3) hue-rotate(720deg) contrast(0.5)
                        drop-shadow(0 0 0px transparent);
            }
        }

        @keyframes neuralEnergyPulse {
            0% {
                filter: brightness(1.3) contrast(1.4) saturate(1.6) hue-rotate(0deg)
                        drop-shadow(0 30px 70px rgba(0, 0, 0, 0.4))
                        drop-shadow(0 0 50px var(--neon-cyan))
                        drop-shadow(0 0 80px var(--hologram-blue));
            }
            100% {
                filter: brightness(1.7) contrast(1.7) saturate(2.0) hue-rotate(120deg)
                        drop-shadow(0 40px 100px rgba(0, 0, 0, 0.6))
                        drop-shadow(0 0 80px var(--neon-purple))
                        drop-shadow(0 0 120px var(--quantum-pink))
                        drop-shadow(0 0 160px var(--neural-green));
            }
        }

        @keyframes neuralDimensionalFloat {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotateZ(0deg) scale(1);
            }
            20% {
                transform: translateY(-8px) translateX(3px) rotateZ(1deg) scale(1.01);
            }
            40% {
                transform: translateY(-12px) translateX(-2px) rotateZ(-0.5deg) scale(1.02);
            }
            60% {
                transform: translateY(-6px) translateX(-4px) rotateZ(1.5deg) scale(1.01);
            }
            80% {
                transform: translateY(-10px) translateX(1px) rotateZ(-1deg) scale(1.015);
            }
        }

        /* Futuristické neural background elementy */
        .neural-orb {
            position: absolute;
            border-radius: 50%;
            background:
                conic-gradient(from 0deg at 50% 50%,
                    var(--neon-cyan) 0deg,
                    transparent 45deg,
                    var(--neon-purple) 90deg,
                    transparent 135deg,
                    var(--neural-green) 180deg,
                    transparent 225deg,
                    var(--hologram-blue) 270deg,
                    transparent 315deg,
                    var(--neon-cyan) 360deg),
                radial-gradient(ellipse at center,
                    rgba(0, 255, 255, 0.6) 0%,
                    rgba(255, 0, 255, 0.4) 30%,
                    rgba(0, 230, 118, 0.3) 60%,
                    transparent 80%);
            animation:
                neuralOrbDrift 25s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite,
                neuralOrbPulse 8s ease-in-out infinite alternate,
                neuralOrbRotation 18s linear infinite;
            pointer-events: none;
            filter: blur(3px);
            box-shadow:
                0 0 60px var(--neon-cyan),
                inset 0 0 50px rgba(0, 255, 255, 0.3),
                0 0 100px var(--neon-purple),
                0 0 140px rgba(0, 230, 118, 0.2);
        }

        .neural-orb:nth-child(1) {
            width: 160px;
            height: 160px;
            top: 12%;
            left: 6%;
            animation-delay: 0s, 0s, 0s;
        }

        .neural-orb:nth-child(2) {
            width: 220px;
            height: 220px;
            top: 68%;
            right: 8%;
            animation-delay: 8s, 4s, 12s;
        }

        .neural-orb:nth-child(3) {
            width: 140px;
            height: 140px;
            bottom: 12%;
            left: 12%;
            animation-delay: 16s, 8s, 24s;
        }

        .neural-orb:nth-child(4) {
            width: 100px;
            height: 100px;
            top: 35%;
            right: 25%;
            animation-delay: 24s, 12s, 36s;
        }

        @keyframes neuralOrbDrift {
            0%, 100% {
                transform: translateY(0px) translateX(0px) translateZ(0px) scale(1) rotate(0deg);
                opacity: 0.7;
            }
            20% {
                transform: translateY(-60px) translateX(40px) translateZ(30px) scale(1.15) rotate(72deg);
                opacity: 0.9;
            }
            40% {
                transform: translateY(-100px) translateX(-30px) translateZ(-20px) scale(0.85) rotate(144deg);
                opacity: 1;
            }
            60% {
                transform: translateY(-70px) translateX(-50px) translateZ(40px) scale(1.25) rotate(216deg);
                opacity: 0.8;
            }
            80% {
                transform: translateY(-30px) translateX(20px) translateZ(-10px) scale(0.95) rotate(288deg);
                opacity: 0.9;
            }
        }

        @keyframes neuralOrbPulse {
            0% {
                box-shadow:
                    0 0 60px var(--neon-cyan),
                    inset 0 0 50px rgba(0, 255, 255, 0.3),
                    0 0 100px var(--neon-purple);
                filter: blur(3px) brightness(1) hue-rotate(0deg) contrast(1.2);
            }
            100% {
                box-shadow:
                    0 0 120px var(--neon-purple),
                    inset 0 0 100px rgba(255, 0, 255, 0.5),
                    0 0 160px var(--neural-green),
                    0 0 200px var(--hologram-blue);
                filter: blur(2px) brightness(1.4) hue-rotate(180deg) contrast(1.5);
            }
        }

        @keyframes neuralOrbRotation {
            0% { transform: rotate(0deg) scale(1); }
            20% { transform: rotate(72deg) scale(1.08); }
            40% { transform: rotate(144deg) scale(0.92); }
            60% { transform: rotate(216deg) scale(1.12); }
            80% { transform: rotate(288deg) scale(0.96); }
            100% { transform: rotate(360deg) scale(1); }
        }

        /* Futuristický responsive design */
        @media (max-width: 768px) {
            .nexus-phone {
                width: 320px;
            }
            .neural-notification {
                width: 260px;
                top: 6%;
            }
            .neural-orb:nth-child(1) { width: 120px; height: 120px; }
            .neural-orb:nth-child(2) { width: 180px; height: 180px; }
            .neural-orb:nth-child(3) { width: 100px; height: 100px; }
            .neural-orb:nth-child(4) { width: 80px; height: 80px; }
        }

        @media (max-width: 480px) {
            .nexus-phone {
                width: 280px;
            }
            .neural-notification {
                width: 220px;
                top: 8%;
            }
            .neural-orb:nth-child(1) { width: 100px; height: 100px; }
            .neural-orb:nth-child(2) { width: 140px; height: 140px; }
            .neural-orb:nth-child(3) { width: 80px; height: 80px; }
            .neural-orb:nth-child(4) { width: 60px; height: 60px; }
        }

        /* Dodatečné futuristické efekty */
        @keyframes neuralScanline {
            0%, 100% {
                background-position: 0% 0%;
                opacity: 0.1;
            }
            50% {
                background-position: 100% 100%;
                opacity: 0.3;
            }
        }

        .neural-scanlines {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                repeating-linear-gradient(
                    0deg,
                    transparent 0px,
                    rgba(0, 255, 255, 0.03) 1px,
                    transparent 2px,
                    transparent 4px
                );
            animation: neuralScanline 8s linear infinite;
            pointer-events: none;
            z-index: 1;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .phone {
                width: 300px;
            }
            .notification {
                width: 220px;
                top: 6%;
            }
        }

        @media (max-width: 480px) {
            .phone {
                width: 250px;
            }
            .notification {
                width: 180px;
                top: 8%;
            }
        }
    </style>
</head>
<body>
    <!-- Neural scanlines efekt -->
    <div class="neural-scanlines"></div>

    <!-- Futuristické neural orbs -->
    <div class="neural-orb"></div>
    <div class="neural-orb"></div>
    <div class="neural-orb"></div>
    <div class="neural-orb"></div>

    <!-- Nexus Phone container s futuristickými animacemi -->
    <div class="nexus-container">
        <!-- Futuristické neural notifikace -->
        <img src="photos/Notifikace 1.svg" alt="Neural Notification 1" class="neural-notification">
        <img src="photos/Notifikace 2.svg" alt="Neural Notification 2" class="neural-notification">
        <img src="photos/Notifikace 3.svg" alt="Neural Notification 3" class="neural-notification">
        <img src="photos/Notifikace 4.svg" alt="Neural Notification 4" class="neural-notification">
        <img src="photos/Notifikace 5.svg" alt="Neural Notification 5" class="neural-notification">
        <img src="photos/Notifikace 6.svg" alt="Neural Notification 6" class="neural-notification">

        <!-- Nexus Phone -->
        <img src="photos/Phone.svg" alt="Nexus Phone" class="nexus-phone">
    </div>

    <script>
        // FUTURISTICKÉ NEURAL NETWORK EFEKTY
        document.addEventListener('DOMContentLoaded', function() {
            const nexusPhone = document.querySelector('.nexus-phone');
            const neuralNotifications = document.querySelectorAll('.neural-notification');
            const nexusContainer = document.querySelector('.nexus-container');

            // Vytvoření futuristického neural particle systému
            function createNeuralParticles() {
                for (let i = 0; i < 80; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'neural-particle';
                    particle.style.left = Math.random() * 100 + 'vw';
                    particle.style.animationDelay = Math.random() * 12 + 's';
                    particle.style.animationDuration = (Math.random() * 10 + 12) + 's';

                    // Futuristické velikosti a neon barvy
                    const size = Math.random() * 8 + 4;
                    particle.style.width = size + 'px';
                    particle.style.height = size + 'px';

                    const colors = ['var(--neon-cyan)', 'var(--neon-purple)', 'var(--neural-green)', 'var(--hologram-blue)'];
                    const randomColor = colors[Math.floor(Math.random() * colors.length)];

                    particle.style.background = `
                        radial-gradient(circle at 30% 30%, ${randomColor} 0%, transparent 40%),
                        conic-gradient(from 0deg, ${randomColor} 0deg, transparent 120deg, ${randomColor} 240deg, transparent 360deg)
                    `;

                    document.body.appendChild(particle);
                }
            }

            // Futuristický quantum click efekt na Nexus Phone
            nexusPhone.addEventListener('click', function(e) {
                // Quantum transformace telefonu
                this.style.transform = 'scale(0.85) rotateX(15deg) rotateY(20deg) translateZ(-40px)';
                this.style.filter = `
                    brightness(1.5) contrast(1.8) saturate(2.5) hue-rotate(60deg)
                    drop-shadow(0 80px 160px rgba(0, 0, 0, 0.8))
                    drop-shadow(0 0 120px var(--neon-cyan))
                    drop-shadow(0 0 180px var(--neon-purple))
                    drop-shadow(0 0 240px var(--neural-green))
                `;

                // Vytvoření quantum burst efektu
                createQuantumBurst(e.clientX, e.clientY);

                // Quantum návrat do původního stavu
                setTimeout(() => {
                    this.style.transform = 'scale(1.15) rotateX(-10deg) rotateY(-15deg) translateZ(20px)';
                }, 200);
                setTimeout(() => {
                    this.style.transform = '';
                    this.style.filter = '';
                }, 800);
            });

            // Funkce pro vytvoření quantum burst efektu
            function createQuantumBurst(x, y) {
                for (let i = 0; i < 40; i++) {
                    const quantumSpark = document.createElement('div');
                    quantumSpark.style.position = 'fixed';
                    quantumSpark.style.left = x + 'px';
                    quantumSpark.style.top = y + 'px';
                    quantumSpark.style.width = Math.random() * 12 + 6 + 'px';
                    quantumSpark.style.height = quantumSpark.style.width;

                    const colors = ['var(--neon-cyan)', 'var(--neon-purple)', 'var(--neural-green)', 'var(--hologram-blue)'];
                    const randomColor = colors[Math.floor(Math.random() * colors.length)];

                    quantumSpark.style.background = `
                        conic-gradient(from 0deg,
                            ${randomColor} 0deg,
                            transparent 120deg,
                            ${randomColor} 240deg,
                            transparent 360deg)
                    `;
                    quantumSpark.style.borderRadius = '50%';
                    quantumSpark.style.pointerEvents = 'none';
                    quantumSpark.style.zIndex = '1000';
                    quantumSpark.style.filter = `blur(${Math.random() * 3}px)`;
                    quantumSpark.style.boxShadow = `0 0 ${Math.random() * 20 + 10}px ${randomColor}`;

                    const angle = (i / 40) * Math.PI * 2;
                    const distance = Math.random() * 200 + 80;
                    const duration = Math.random() * 0.8 + 0.6;

                    quantumSpark.style.animation = `quantumExplode ${duration}s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards`;
                    quantumSpark.style.setProperty('--angle', angle + 'rad');
                    quantumSpark.style.setProperty('--distance', distance + 'px');

                    document.body.appendChild(quantumSpark);
                    setTimeout(() => quantumSpark.remove(), duration * 1000);
                }
            }

            // Futuristické neural hover efekty pro notifikace
            neuralNotifications.forEach((notification, index) => {
                notification.addEventListener('mouseenter', function() {
                    this.style.animationPlayState = 'paused';
                    this.style.transform = `
                        translateX(-50%) translateY(0px) translateZ(30px)
                        scale(1.12) rotateX(-5deg) rotateY(${8 - index * 2}deg) rotateZ(${2 - index * 0.8}deg)
                    `;
                    this.style.filter = `
                        brightness(1.8) contrast(1.6) saturate(2.2) hue-rotate(${index * 60}deg)
                        drop-shadow(0 40px 80px rgba(0, 0, 0, 0.6))
                        drop-shadow(0 0 80px var(--neon-cyan))
                        drop-shadow(0 0 120px var(--neon-purple))
                        drop-shadow(0 0 160px var(--neural-green))
                    `;
                    this.style.zIndex = '100';

                    // Vytvoření neural glow efektu kolem notifikace
                    createNeuralGlow(this, index);
                });

                notification.addEventListener('mouseleave', function() {
                    this.style.animationPlayState = 'running';
                    this.style.transform = '';
                    this.style.filter = '';
                    this.style.zIndex = '20';

                    // Odstranění neural glow efektu
                    const glow = this.querySelector('.neural-glow');
                    if (glow) glow.remove();
                });

                // Quantum click efekt na notifikace
                notification.addEventListener('click', function(e) {
                    this.style.transform = `
                        translateX(-50%) translateY(0px) translateZ(-20px)
                        scale(0.9) rotateX(8deg) rotateY(15deg) rotateZ(5deg)
                    `;

                    // Vytvoření quantum ripple efektu
                    createQuantumRipple(e.target, e.offsetX, e.offsetY);

                    setTimeout(() => {
                        this.style.transform = `
                            translateX(-50%) translateY(0px) translateZ(25px)
                            scale(1.08) rotateX(-4deg) rotateY(-8deg) rotateZ(-2deg)
                        `;
                    }, 150);
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 600);
                });
            });

            // Funkce pro vytvoření neural glow efektu kolem notifikace
            function createNeuralGlow(notification, index) {
                const glow = document.createElement('div');
                glow.className = 'neural-glow';
                glow.style.position = 'absolute';
                glow.style.top = '-15px';
                glow.style.left = '-15px';
                glow.style.right = '-15px';
                glow.style.bottom = '-15px';

                const colors = ['var(--neon-cyan)', 'var(--neon-purple)', 'var(--neural-green)', 'var(--hologram-blue)'];
                const glowColor = colors[index % colors.length];

                glow.style.background = `
                    conic-gradient(from 0deg,
                        ${glowColor} 0deg,
                        transparent 60deg,
                        ${glowColor} 120deg,
                        transparent 180deg,
                        ${glowColor} 240deg,
                        transparent 300deg,
                        ${glowColor} 360deg)
                `;
                glow.style.borderRadius = '30px';
                glow.style.filter = 'blur(15px)';
                glow.style.zIndex = '-1';
                glow.style.animation = 'neuralGlowPulse 3s ease-in-out infinite alternate';

                notification.style.position = 'relative';
                notification.appendChild(glow);
            }

            // Funkce pro vytvoření quantum ripple efektu
            function createQuantumRipple(element, x, y) {
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.style.width = '10px';
                ripple.style.height = '10px';
                ripple.style.background = `
                    conic-gradient(from 0deg,
                        var(--neon-cyan) 0deg,
                        var(--neon-purple) 120deg,
                        var(--neural-green) 240deg,
                        var(--neon-cyan) 360deg)
                `;
                ripple.style.borderRadius = '50%';
                ripple.style.transform = 'translate(-50%, -50%)';
                ripple.style.pointerEvents = 'none';
                ripple.style.zIndex = '1000';
                ripple.style.animation = 'quantumRipple 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards';
                ripple.style.filter = 'blur(1px)';

                element.appendChild(ripple);
                setTimeout(() => ripple.remove(), 1200);
            }

            // Futuristický neural parallax efekt při pohybu myši
            document.addEventListener('mousemove', function(e) {
                const x = (e.clientX / window.innerWidth - 0.5) * 2;
                const y = (e.clientY / window.innerHeight - 0.5) * 2;

                nexusContainer.style.transform = `
                    perspective(2000px)
                    rotateY(${x * 8}deg)
                    rotateX(${-y * 8}deg)
                    translateZ(${Math.abs(x) * 10}px)
                `;

                // Neural parallax pro orbs
                document.querySelectorAll('.neural-orb').forEach((orb, index) => {
                    const speed = (index + 1) * 0.8;
                    const rotationSpeed = (index + 1) * 2;
                    orb.style.transform += `
                        translate(${x * speed * 15}px, ${y * speed * 15}px)
                        rotate(${x * rotationSpeed * 10}deg)
                        scale(${1 + Math.abs(x) * 0.1})
                    `;
                });

                // Neural scanlines reaktivní efekt
                const scanlines = document.querySelector('.neural-scanlines');
                if (scanlines) {
                    scanlines.style.opacity = 0.1 + Math.abs(x) * 0.2;
                    scanlines.style.filter = `hue-rotate(${x * 180}deg)`;
                }
            });

            // Spuštění futuristického neural particle systému
            createNeuralParticles();
        });

        // CSS pro futuristické neural animace
        const neuralStyles = document.createElement('style');
        neuralStyles.textContent = `
            @keyframes quantumExplode {
                0% {
                    transform: translate(-50%, -50%) scale(1) rotate(0deg);
                    opacity: 1;
                    filter: blur(0px) hue-rotate(0deg);
                }
                50% {
                    transform: translate(
                        calc(-50% + cos(var(--angle)) * calc(var(--distance) * 0.6)),
                        calc(-50% + sin(var(--angle)) * calc(var(--distance) * 0.6))
                    ) scale(1.2) rotate(180deg);
                    opacity: 0.8;
                    filter: blur(2px) hue-rotate(180deg);
                }
                100% {
                    transform: translate(
                        calc(-50% + cos(var(--angle)) * var(--distance)),
                        calc(-50% + sin(var(--angle)) * var(--distance))
                    ) scale(0) rotate(360deg);
                    opacity: 0;
                    filter: blur(5px) hue-rotate(360deg);
                }
            }

            @keyframes neuralGlowPulse {
                0% {
                    opacity: 0.4;
                    transform: scale(1) rotate(0deg);
                    filter: blur(15px) hue-rotate(0deg);
                }
                100% {
                    opacity: 0.8;
                    transform: scale(1.08) rotate(180deg);
                    filter: blur(12px) hue-rotate(120deg);
                }
            }

            @keyframes quantumRipple {
                0% {
                    width: 10px;
                    height: 10px;
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1) rotate(0deg);
                    filter: blur(0px) hue-rotate(0deg);
                }
                50% {
                    width: 150px;
                    height: 150px;
                    opacity: 0.6;
                    transform: translate(-50%, -50%) scale(1) rotate(180deg);
                    filter: blur(3px) hue-rotate(180deg);
                }
                100% {
                    width: 300px;
                    height: 300px;
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(1) rotate(360deg);
                    filter: blur(8px) hue-rotate(360deg);
                }
            }

            @keyframes neuralQuantumSpin {
                0% { transform: rotate(0deg) scale(1) skew(0deg); }
                25% { transform: rotate(90deg) scale(1.15) skew(5deg); }
                50% { transform: rotate(180deg) scale(0.85) skew(-3deg); }
                75% { transform: rotate(270deg) scale(1.08) skew(8deg); }
                100% { transform: rotate(360deg) scale(1) skew(0deg); }
            }
        `;
        document.head.appendChild(neuralStyles);

        // Futuristický Neural Quantum Particle systém
        class NeuralQuantumParticleSystem {
            constructor() {
                this.particles = [];
                this.maxParticles = 80;
                this.neuralConnections = [];
                this.init();
            }

            init() {
                setInterval(() => this.createNeuralQuantumParticle(), 300);
                setInterval(() => this.createHolographicParticle(), 600);
                setInterval(() => this.createNeuralConnection(), 1200);
                this.animate();
            }

            createNeuralQuantumParticle() {
                if (this.particles.length >= this.maxParticles) return;

                const particle = document.createElement('div');
                const size = Math.random() * 15 + 8;
                const colors = ['var(--neon-cyan)', 'var(--neon-purple)', 'var(--neural-green)', 'var(--hologram-blue)'];
                const randomColor = colors[Math.floor(Math.random() * colors.length)];

                particle.style.cssText = `
                    position: fixed;
                    left: ${Math.random() * 100}vw;
                    top: 100vh;
                    width: ${size}px;
                    height: ${size}px;
                    background:
                        conic-gradient(from 0deg,
                            ${randomColor} 0deg,
                            transparent 60deg,
                            ${randomColor} 120deg,
                            transparent 180deg,
                            ${randomColor} 240deg,
                            transparent 300deg,
                            ${randomColor} 360deg),
                        radial-gradient(circle at center,
                            rgba(255, 255, 255, 0.8) 0%,
                            transparent 60%);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 5;
                    filter: blur(${Math.random() * 3}px);
                    box-shadow:
                        0 0 ${size * 3}px ${randomColor},
                        inset 0 0 ${size}px rgba(255, 255, 255, 0.4);
                    animation: neuralQuantumSpin ${Math.random() * 8 + 6}s linear infinite;
                `;

                document.body.appendChild(particle);
                this.particles.push({
                    element: particle,
                    x: Math.random() * window.innerWidth,
                    y: window.innerHeight,
                    vx: (Math.random() - 0.5) * 3,
                    vy: -(Math.random() * 4 + 3),
                    life: 1,
                    decay: Math.random() * 0.008 + 0.003,
                    color: randomColor
                });
            }

            createHolographicParticle() {
                const particle = document.createElement('div');
                const size = Math.random() * 10 + 6;
                const colors = ['var(--neon-cyan)', 'var(--neon-purple)', 'var(--neural-green)'];
                const randomColor = colors[Math.floor(Math.random() * colors.length)];

                particle.style.cssText = `
                    position: fixed;
                    left: ${Math.random() * 100}vw;
                    top: 100vh;
                    width: ${size}px;
                    height: ${size}px;
                    background:
                        radial-gradient(circle at 30% 30%,
                            rgba(255, 255, 255, 0.9) 0%,
                            ${randomColor} 40%,
                            transparent 80%),
                        linear-gradient(45deg,
                            ${randomColor} 0%,
                            transparent 50%,
                            ${randomColor} 100%);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 6;
                    animation: neuralFloat ${Math.random() * 15 + 10}s ease-in-out infinite;
                    filter: blur(${Math.random() * 2}px);
                    box-shadow:
                        0 0 ${size * 5}px ${randomColor},
                        inset 0 0 ${size * 2}px rgba(255, 255, 255, 0.6);
                `;

                document.body.appendChild(particle);

                setTimeout(() => {
                    particle.remove();
                }, 25000);
            }

            createNeuralConnection() {
                if (this.particles.length < 2) return;

                const connection = document.createElement('div');
                const particle1 = this.particles[Math.floor(Math.random() * this.particles.length)];
                const particle2 = this.particles[Math.floor(Math.random() * this.particles.length)];

                if (particle1 === particle2) return;

                const x1 = particle1.x;
                const y1 = particle1.y;
                const x2 = particle2.x;
                const y2 = particle2.y;

                const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
                const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

                connection.style.cssText = `
                    position: fixed;
                    left: ${x1}px;
                    top: ${y1}px;
                    width: ${distance}px;
                    height: 2px;
                    background: linear-gradient(90deg,
                        ${particle1.color} 0%,
                        transparent 50%,
                        ${particle2.color} 100%);
                    transform: rotate(${angle}deg);
                    transform-origin: 0 50%;
                    pointer-events: none;
                    z-index: 4;
                    opacity: 0.3;
                    filter: blur(1px);
                    animation: neuralConnectionPulse 2s ease-in-out forwards;
                `;

                document.body.appendChild(connection);
                this.neuralConnections.push(connection);

                setTimeout(() => {
                    connection.remove();
                    this.neuralConnections = this.neuralConnections.filter(c => c !== connection);
                }, 2000);
            }

            animate() {
                this.particles = this.particles.filter(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    particle.life -= particle.decay;

                    // Přidání mírné sinusové oscilace pro organický pohyb
                    const time = Date.now() / 1000;
                    const oscillationX = Math.sin(time + particle.x) * 1.5;
                    const oscillationY = Math.cos(time + particle.y) * 1.5;

                    particle.element.style.left = (particle.x + oscillationX) + 'px';
                    particle.element.style.top = (particle.y + oscillationY) + 'px';
                    particle.element.style.opacity = particle.life;

                    // Dynamická změna velikosti a filtru
                    const scale = 0.8 + Math.sin(time * 2 + particle.x) * 0.2;
                    const hueRotate = Math.sin(time + particle.y) * 30;
                    particle.element.style.transform += ` scale(${scale})`;
                    particle.element.style.filter += ` hue-rotate(${hueRotate}deg)`;

                    if (particle.life <= 0 || particle.y < -50) {
                        particle.element.remove();
                        return false;
                    }
                    return true;
                });

                // Aktualizace neural connections
                this.neuralConnections.forEach(connection => {
                    connection.style.opacity = 0.3 + Math.sin(Date.now() / 500) * 0.2;
                });

                requestAnimationFrame(() => this.animate());
            }
        }

        // Přidání CSS pro neural connection pulse
        neuralStyles.textContent += `
            @keyframes neuralConnectionPulse {
                0% { opacity: 0; width: 0; }
                50% { opacity: 0.5; }
                100% { opacity: 0; }
            }
        `;

        // Spuštění neural quantum particle systému
        new NeuralQuantumParticleSystem();
    </script>
</body>
</html>
