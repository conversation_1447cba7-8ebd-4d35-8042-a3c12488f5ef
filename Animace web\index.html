<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iPhone Notifikace</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            min-height: 100vh;
            background: #D9F1FF;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }

        .phone-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .phone {
            width: 350px;
            height: auto;
            filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.15));
            transition: transform 0.3s ease;
            z-index: 1;
        }

        .phone:hover {
            transform: scale(1.02);
        }

        .notification {
            position: absolute;
            top: 4%;
            left: 55%;
            transform: translateX(-50%) translateY(-70px);
            width: 290px;
            height: auto;
            opacity: 0;
            filter: brightness(1.4) contrast(0.95) drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
            z-index: 2;
            pointer-events: none;
        }

        .notification:nth-child(1) {
            animation: realIphoneSlide 24s infinite;
            animation-delay: 0s;
        }

        .notification:nth-child(2) {
            animation: realIphoneSlide 24s infinite;
            animation-delay: 4s;
        }

        .notification:nth-child(3) {
            animation: realIphoneSlide 24s infinite;
            animation-delay: 8s;
        }

        .notification:nth-child(4) {
            animation: realIphoneSlide 24s infinite;
            animation-delay: 12s;
        }

        .notification:nth-child(5) {
            animation: realIphoneSlide 24s infinite;
            animation-delay: 16s;
        }

        .notification:nth-child(6) {
            animation: realIphoneSlide 24s infinite;
            animation-delay: 20s;
        }



        @keyframes realIphoneSlide {
            0% {
                opacity: 0;
                transform: translateX(-50%) translateY(-70px);
            }
            4.17% {
                opacity: 1;
                transform: translateX(-50%) translateY(0px);
            }
            12.5% {
                opacity: 1;
                transform: translateX(-50%) translateY(0px);
            }
            16.67% {
                opacity: 0;
                transform: translateX(-50%) translateY(-70px);
            }
            100% {
                opacity: 0;
                transform: translateX(-50%) translateY(-70px);
            }
        }

        /* Floating background elements */
        .bg-element {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            animation: float 8s ease-in-out infinite;
        }

        .bg-element:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 15%;
            left: 8%;
            animation-delay: 0s;
        }

        .bg-element:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 65%;
            right: 10%;
            animation-delay: 3s;
        }

        .bg-element:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 15%;
            left: 15%;
            animation-delay: 6s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.4;
            }
            50% {
                transform: translateY(-30px) rotate(180deg);
                opacity: 0.7;
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .phone {
                width: 300px;
            }

            .notification {
                width: 220px;
                top: 9%;
            }
        }

        @media (max-width: 480px) {
            .phone {
                width: 250px;
            }

            .notification {
                width: 180px;
                top: 10%;
            }
        }
    </style>
</head>
<body>
    <!-- Background floating elements -->
    <div class="bg-element"></div>
    <div class="bg-element"></div>
    <div class="bg-element"></div>

    <div class="phone-container">
        <!-- Notifications -->
        <img src="photos/Notifikace 1.svg" alt="Notifikace 1" class="notification">
        <img src="photos/Notifikace 2.svg" alt="Notifikace 2" class="notification">
        <img src="photos/Notifikace 3.svg" alt="Notifikace 3" class="notification">
        <img src="photos/Notifikace 4.svg" alt="Notifikace 4" class="notification">
        <img src="photos/Notifikace 5.svg" alt="Notifikace 5" class="notification">
        <img src="photos/Notifikace 6.svg" alt="Notifikace 6" class="notification">
        
        <!-- Phone -->
        <img src="photos/Phone.svg" alt="iPhone" class="phone">
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const phone = document.querySelector('.phone');
            const notifications = document.querySelectorAll('.notification');
            
            // Add click effect to phone
            phone.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1.05)';
                }, 100);
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 200);
            });

            // Add hover effect to notifications
            notifications.forEach(notification => {
                notification.addEventListener('mouseenter', function() {
                    this.style.animationPlayState = 'paused';
                    this.style.transform = 'translateX(-50%) translateY(0px) scale(1.02)';
                });

                notification.addEventListener('mouseleave', function() {
                    this.style.animationPlayState = 'running';
                    this.style.transform = 'translateX(-50%) translateY(0px) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
