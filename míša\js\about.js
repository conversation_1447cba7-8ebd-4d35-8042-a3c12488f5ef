// About Page Specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // FAQ Accordion functionality
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        
        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');
            
            // Close all other FAQ items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                    const otherAnswer = otherItem.querySelector('.faq-answer');
                    otherAnswer.style.maxHeight = '0';
                }
            });
            
            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
                answer.style.maxHeight = '0';
            } else {
                item.classList.add('active');
                answer.style.maxHeight = answer.scrollHeight + 'px';
            }
        });
    });

    // Value cards animation on scroll
    const valueCards = document.querySelectorAll('.value-card');
    
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const valueObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 200);
            }
        });
    }, observerOptions);

    valueCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        valueObserver.observe(card);
    });

    // Social cards hover effects
    const socialCards = document.querySelectorAll('.social-card');
    
    socialCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('i');
            icon.style.transform = 'scale(1.2) rotate(5deg)';
            icon.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('i');
            icon.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // Smooth reveal animation for story content
    const storyParagraphs = document.querySelectorAll('.story-content p');
    
    const storyObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 300);
            }
        });
    }, observerOptions);

    storyParagraphs.forEach(paragraph => {
        paragraph.style.opacity = '0';
        paragraph.style.transform = 'translateY(20px)';
        paragraph.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        storyObserver.observe(paragraph);
    });

    // Hero image parallax effect
    const heroImage = document.querySelector('.about-hero-image img');
    
    if (heroImage) {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = scrolled * 0.3;
            
            heroImage.style.transform = `translateY(${parallax}px)`;
        });
    }

    // Add loading animation to hero image
    if (heroImage) {
        heroImage.style.opacity = '0';
        heroImage.style.transform = 'scale(1.1)';
        heroImage.style.transition = 'opacity 1s ease, transform 1s ease';
        
        heroImage.onload = () => {
            heroImage.style.opacity = '1';
            heroImage.style.transform = 'scale(1)';
        };
        
        // Fallback if image is already loaded
        if (heroImage.complete) {
            heroImage.style.opacity = '1';
            heroImage.style.transform = 'scale(1)';
        }
    }

    // Add click tracking for social links (for analytics)
    socialCards.forEach(card => {
        card.addEventListener('click', function(e) {
            const platform = this.querySelector('h3').textContent;
            console.log(`Social link clicked: ${platform}`);
            
            // Here you could add analytics tracking
            // gtag('event', 'social_click', { platform: platform });
        });
    });

    // Add subtle animation to value icons
    const valueIcons = document.querySelectorAll('.value-icon');
    
    valueIcons.forEach(icon => {
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.2) rotate(10deg)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // FAQ search functionality (if needed in future)
    const addFAQSearch = () => {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = 'Hledat v často kladených otázkách...';
        searchInput.className = 'faq-search';
        
        const faqSection = document.querySelector('.faq .container');
        const faqTitle = faqSection.querySelector('.section-title');
        
        faqTitle.insertAdjacentElement('afterend', searchInput);
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question').textContent.toLowerCase();
                const answer = item.querySelector('.faq-answer').textContent.toLowerCase();
                
                if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    };

    // Uncomment to enable FAQ search
    // addFAQSearch();

    // Add reading progress indicator for long content
    const addReadingProgress = () => {
        const progressBar = document.createElement('div');
        progressBar.className = 'reading-progress';
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: var(--accent-rose);
            z-index: 9999;
            transition: width 0.3s ease;
        `;
        
        document.body.appendChild(progressBar);
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            
            progressBar.style.width = scrollPercent + '%';
        });
    };

    addReadingProgress();

    // Console message for about page
    console.log('%c📖 Díky, že si čtete o mně! 📖', 'color: #d4a574; font-size: 14px; font-weight: bold;');
});
