// Blog Page Specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const filterButtons = document.querySelectorAll('.filter-btn');
    const searchInput = document.querySelector('.search-input');
    const sortSelect = document.querySelector('.sort-select');
    const articlesGrid = document.getElementById('articles-grid');
    const loadMoreBtn = document.querySelector('.load-more-btn');
    const articleCards = document.querySelectorAll('.article-card');

    // Store original articles for filtering
    let allArticles = Array.from(articleCards);
    let filteredArticles = [...allArticles];
    let currentCategory = 'all';
    let currentSearchTerm = '';

    // Category filtering
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            currentCategory = this.dataset.category;
            filterArticles();
        });
    });

    // Search functionality
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        currentSearchTerm = this.value.toLowerCase().trim();
        
        searchTimeout = setTimeout(() => {
            filterArticles();
        }, 300);
    });

    // Sort functionality
    sortSelect.addEventListener('change', function() {
        const sortBy = this.value;
        sortArticles(sortBy);
    });

    // Filter articles based on category and search
    function filterArticles() {
        filteredArticles = allArticles.filter(article => {
            const category = article.dataset.category;
            const title = article.querySelector('.article-title').textContent.toLowerCase();
            const excerpt = article.querySelector('.article-excerpt').textContent.toLowerCase();
            const tags = Array.from(article.querySelectorAll('.tag')).map(tag => tag.textContent.toLowerCase());
            
            // Category filter
            const categoryMatch = currentCategory === 'all' || category === currentCategory;
            
            // Search filter
            const searchMatch = currentSearchTerm === '' || 
                title.includes(currentSearchTerm) || 
                excerpt.includes(currentSearchTerm) ||
                tags.some(tag => tag.includes(currentSearchTerm));
            
            return categoryMatch && searchMatch;
        });

        displayArticles();
    }

    // Sort articles
    function sortArticles(sortBy) {
        switch(sortBy) {
            case 'newest':
                filteredArticles.sort((a, b) => {
                    const dateA = new Date(a.dataset.date);
                    const dateB = new Date(b.dataset.date);
                    return dateB - dateA;
                });
                break;
            case 'oldest':
                filteredArticles.sort((a, b) => {
                    const dateA = new Date(a.dataset.date);
                    const dateB = new Date(b.dataset.date);
                    return dateA - dateB;
                });
                break;
            case 'popular':
                // Simulate popularity sorting (in real app, this would be based on actual data)
                filteredArticles.sort(() => Math.random() - 0.5);
                break;
        }

        displayArticles();
    }

    // Display filtered articles
    function displayArticles() {
        // Hide all articles first
        allArticles.forEach(article => {
            article.style.display = 'none';
            article.classList.add('fade-out');
        });

        // Show filtered articles
        setTimeout(() => {
            if (filteredArticles.length === 0) {
                showNoResults();
            } else {
                hideNoResults();
                filteredArticles.forEach((article, index) => {
                    setTimeout(() => {
                        article.style.display = 'block';
                        article.classList.remove('fade-out');
                        article.classList.add('fade-in');
                    }, index * 100);
                });
            }
        }, 300);
    }

    // Show no results message
    function showNoResults() {
        let noResultsDiv = document.querySelector('.no-results');
        if (!noResultsDiv) {
            noResultsDiv = document.createElement('div');
            noResultsDiv.className = 'no-results';
            noResultsDiv.innerHTML = `
                <h3>Žádné články nenalezeny</h3>
                <p>Zkuste změnit vyhledávací kritéria nebo kategorii.</p>
                <button class="btn" onclick="clearFilters()">Vymazat filtry</button>
            `;
            articlesGrid.appendChild(noResultsDiv);
        }
        noResultsDiv.style.display = 'block';
    }

    // Hide no results message
    function hideNoResults() {
        const noResultsDiv = document.querySelector('.no-results');
        if (noResultsDiv) {
            noResultsDiv.style.display = 'none';
        }
    }

    // Clear all filters
    window.clearFilters = function() {
        searchInput.value = '';
        currentSearchTerm = '';
        currentCategory = 'all';
        
        filterButtons.forEach(btn => btn.classList.remove('active'));
        document.querySelector('[data-category="all"]').classList.add('active');
        
        sortSelect.value = 'newest';
        
        filterArticles();
    };

    // Load more functionality (simulate loading more articles)
    let articlesLoaded = 6;
    const articlesPerLoad = 3;

    loadMoreBtn.addEventListener('click', function() {
        this.textContent = 'Načítám...';
        this.disabled = true;

        // Simulate API call
        setTimeout(() => {
            // In a real app, you would fetch more articles from an API
            // For demo, we'll just show a message
            this.textContent = 'Všechny články načteny';
            this.style.background = '#4CAF50';
            
            setTimeout(() => {
                this.style.display = 'none';
            }, 2000);
        }, 1000);
    });

    // Article card hover effects
    articleCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const image = this.querySelector('.article-image');
            const category = this.querySelector('.article-category');
            
            if (image) {
                image.style.transform = 'scale(1.05)';
            }
            if (category) {
                category.style.transform = 'scale(1.1)';
            }
        });

        card.addEventListener('mouseleave', function() {
            const image = this.querySelector('.article-image');
            const category = this.querySelector('.article-category');
            
            if (image) {
                image.style.transform = 'scale(1)';
            }
            if (category) {
                category.style.transform = 'scale(1)';
            }
        });
    });

    // Tag click functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('tag')) {
            const tagText = e.target.textContent.toLowerCase();
            searchInput.value = tagText;
            currentSearchTerm = tagText;
            filterArticles();
            
            // Scroll to top of articles
            document.querySelector('.blog-filters').scrollIntoView({
                behavior: 'smooth'
            });
        }
    });

    // Reading progress for articles
    function addReadingProgress() {
        const progressBar = document.createElement('div');
        progressBar.className = 'reading-progress';
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: var(--accent-rose);
            z-index: 9999;
            transition: width 0.3s ease;
        `;
        
        document.body.appendChild(progressBar);
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            
            progressBar.style.width = scrollPercent + '%';
        });
    }

    addReadingProgress();

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            searchInput.focus();
        }
        
        // Escape to clear search
        if (e.key === 'Escape' && document.activeElement === searchInput) {
            searchInput.value = '';
            currentSearchTerm = '';
            filterArticles();
            searchInput.blur();
        }
    });

    // Analytics tracking (placeholder)
    function trackEvent(action, category, label) {
        console.log(`Analytics: ${action} - ${category} - ${label}`);
        // In real app: gtag('event', action, { category, label });
    }

    // Track filter usage
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            trackEvent('filter_click', 'blog', this.dataset.category);
        });
    });

    // Track search usage
    searchInput.addEventListener('input', function() {
        if (this.value.length > 2) {
            trackEvent('search', 'blog', this.value);
        }
    });

    // Track article clicks
    articleCards.forEach(card => {
        card.addEventListener('click', function() {
            const title = this.querySelector('.article-title').textContent;
            trackEvent('article_click', 'blog', title);
        });
    });

    // Initialize with default sort
    sortArticles('newest');

    // Console message
    console.log('%c📚 Blog načten! Použijte Ctrl+K pro vyhledávání 📚', 'color: #d4a574; font-size: 14px; font-weight: bold;');
});
