'use client';

import { useRef, useEffect, useState } from 'react';

interface StatItem {
  value: string;
  label: string;
  description: string;
}

const stats: StatItem[] = [
  {
    value: '30%',
    label: 'Úspora času',
    description: 'Automatizace procesů'
  },
  {
    value: '93%',
    label: 'Spokojenos<PERSON>',
    description: 'Hodnocení uživatelů'
  },
  {
    value: '55%',
    label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    description: 'Zpracování plateb'
  }
];

function AnimatedCounter({ value }: { value: string }) {
  return <span>{value}</span>;
}

export default function StatsSection() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Měříme výkon pro vás
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Naše platforma př<PERSON>áší m<PERSON>, kter<PERSON> pomohou vašemu pod<PERSON> růst rychleji a efektivněji.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
                <div className="text-5xl font-bold text-emerald-600 mb-4">
                  <AnimatedCounter value={stat.value} />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {stat.label}
                </h3>
                <p className="text-gray-600">
                  {stat.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="text-left">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Proč si vybrat naši platformu?
                </h3>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                    Pokročilé zabezpečení a šifrování
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                    24/7 zákaznická podpora
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                    Integrace s bankami a službami
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                    Mobilní aplikace pro iOS a Android
                  </li>
                </ul>
              </div>
              <div className="relative">
                <motion.div
                  animate={{ 
                    rotate: [0, 360],
                  }}
                  transition={{ 
                    duration: 20, 
                    repeat: Infinity, 
                    ease: "linear" 
                  }}
                  className="w-32 h-32 mx-auto"
                >
                  <div className="w-full h-full border-4 border-emerald-200 rounded-full relative">
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-emerald-500 rounded-full"></div>
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-3 h-3 bg-blue-500 rounded-full"></div>
                    <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-purple-500 rounded-full"></div>
                    <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-orange-500 rounded-full"></div>
                  </div>
                </motion.div>
                <div className="text-center mt-4">
                  <p className="text-sm text-gray-500">Neustálé inovace</p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
