'use client';

import { useRef, useState } from 'react';

interface Testimonial {
  id: number;
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  avatar: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'CFO',
    company: 'TechStart s.r.o.',
    content: 'Nejlepší systém pro správu financí, který jsme kdy používali. Ušetřil nám hodiny práce týdně a výrazně zlepšil naši cash flow analýzu.',
    rating: 5,
    avatar: 'JN'
  },
  {
    id: 2,
    name: '<PERSON>r Svoboda',
    role: 'Podnikatel',
    company: 'Svoboda Consulting',
    content: 'Díky této platformě mám konečně přehled o všech svých financích na jednom místě. Doporučuji každému, kdo chce mít kontrolu nad svými penězi.',
    rating: 5,
    avatar: 'PS'
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Finanční ředitelka',
    company: 'Green Energy a.s.',
    content: 'Automatizace procesů a AI doporučení nám pomohly optimalizovat investice a zvýšit ziskovost o 25%. Fantastický nástroj!',
    rating: 5,
    avatar: 'MD'
  }
];

export default function TestimonialsSection() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Klienti nás milují
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Přečtěte si, co říkají naši spokojení klienti o naší platformě a jak jim pomohla zlepšit jejich finanční situaci.
          </p>
        </div>

        {/* Main Testimonial Display */}
        <div className="relative max-w-4xl mx-auto mb-12">
          <div className="bg-white rounded-3xl p-12 shadow-2xl relative overflow-hidden">
            {/* Quote Icon */}
            <div className="absolute top-8 left-8 w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center animate-pulse">
              <span className="text-2xl text-emerald-600">💬</span>
            </div>

            {/* Testimonial Content */}
            <div key={currentTestimonial} className="text-center pt-8 transition-all duration-500">
              {/* Stars */}
              <div className="flex justify-center mb-6">
                {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                  <div key={i}>
                    <span className="text-2xl text-yellow-400">⭐</span>
                  </div>
                ))}
              </div>

              {/* Content */}
              <blockquote className="text-2xl text-gray-700 leading-relaxed mb-8 italic">
                "{testimonials[currentTestimonial].content}"
              </blockquote>

              {/* Author */}
              <div className="flex items-center justify-center space-x-4">
                <div className="w-16 h-16 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  {testimonials[currentTestimonial].avatar}
                </div>
                <div className="text-left">
                  <div className="font-bold text-gray-900 text-lg">
                    {testimonials[currentTestimonial].name}
                  </div>
                  <div className="text-gray-600">
                    {testimonials[currentTestimonial].role}
                  </div>
                  <div className="text-emerald-600 font-semibold">
                    {testimonials[currentTestimonial].company}
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Arrows */}
            <button
              onClick={prevTestimonial}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 hover:scale-110 transition-all"
            >
              <span className="text-gray-600 text-xl">←</span>
            </button>
            <button
              onClick={nextTestimonial}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 hover:scale-110 transition-all"
            >
              <span className="text-gray-600 text-xl">→</span>
            </button>

            {/* Background decoration */}
            <div className="absolute bottom-0 right-0 w-32 h-32 bg-emerald-50 rounded-full -translate-y-16 translate-x-16 opacity-50"></div>
          </div>
        </div>

        {/* Testimonial Indicators */}
        <div className="flex justify-center space-x-3 mb-12">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentTestimonial ? 'bg-emerald-600' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>

        {/* Stats Grid */}
        <div className="grid md:grid-cols-4 gap-8">
          {[
            { number: '10,000+', label: 'Spokojených klientů' },
            { number: '99.9%', label: 'Dostupnost systému' },
            { number: '24/7', label: 'Zákaznická podpora' },
            { number: '4.9/5', label: 'Průměrné hodnocení' }
          ].map((stat, index) => (
            <div
              key={index}
              className="text-center bg-white rounded-2xl p-6 shadow-lg hover:scale-105 transition-transform"
            >
              <div className="text-3xl font-bold text-emerald-600 mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 text-sm">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Trust Badges */}
        <div className="mt-16 text-center">
          <p className="text-gray-500 mb-8">Důvěřují nám přední společnosti</p>
          <div className="flex justify-center items-center space-x-12 opacity-60">
            {['TechCorp', 'InnovateLab', 'FutureBank', 'SmartBiz', 'GreenTech'].map((company, index) => (
              <div
                key={index}
                className="text-xl font-bold text-gray-400 cursor-pointer hover:scale-110 hover:opacity-100 transition-all"
              >
                {company}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
