/* About Page Specific Styles */

/* About Hero */
.about-hero {
    padding: 8rem 0 6rem;
    background: linear-gradient(135deg, var(--primary-cream) 0%, var(--warm-gray) 100%);
}

.about-hero-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 4rem;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
}

.about-hero-image {
    position: relative;
}

.about-hero-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 60px var(--soft-shadow);
}

.about-hero-text h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.about-subtitle {
    font-size: 1.3rem;
    color: var(--text-medium);
    line-height: 1.6;
    font-style: italic;
}

/* About Story */
.about-story {
    background: var(--soft-white);
}

.story-content {
    max-width: 800px;
    margin: 0 auto;
}

.story-content h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--text-dark);
}

.story-content p {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    color: var(--text-medium);
}

/* Values */
.values {
    background: var(--warm-gray);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2.5rem;
}

.value-card {
    background: var(--soft-white);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px var(--gentle-shadow);
    transition: all 0.3s ease;
}

.value-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px var(--hover-shadow);
}

.value-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    display: block;
}

.value-card h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.value-card p {
    color: var(--text-medium);
    line-height: 1.6;
}

/* FAQ */
.faq {
    background: var(--soft-white);
}

.faq-content {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--warm-gray);
    padding-bottom: 2rem;
}

.faq-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.faq-question {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    cursor: pointer;
    position: relative;
    padding-right: 2rem;
    transition: color 0.3s ease;
}

.faq-question:hover {
    color: var(--accent-rose);
}

.faq-question::after {
    content: '+';
    position: absolute;
    right: 0;
    top: 0;
    font-size: 1.5rem;
    color: var(--accent-rose);
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question::after {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-medium);
    line-height: 1.7;
    padding-top: 1rem;
}

/* Social Media */
.social-media {
    background: var(--warm-gray);
}

.social-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: var(--text-medium);
    margin-bottom: 3rem;
}

.social-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.social-card {
    background: var(--soft-white);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px var(--gentle-shadow);
}

.social-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px var(--hover-shadow);
}

.social-card i {
    font-size: 3rem;
    color: var(--accent-rose);
    margin-bottom: 1.5rem;
    display: block;
}

.social-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.social-card p {
    font-weight: 600;
    color: var(--accent-rose);
    margin-bottom: 0.5rem;
}

.social-card span {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Active navigation link */
.nav-link.active {
    color: var(--accent-rose);
}

.nav-link.active::after {
    width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .about-hero {
        padding: 6rem 0 4rem;
    }

    .about-hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .about-hero-text h1 {
        font-size: 2.5rem;
    }

    .about-hero-image img {
        height: 400px;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .social-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .faq-question {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .about-hero-text h1 {
        font-size: 2rem;
    }

    .about-subtitle {
        font-size: 1.1rem;
    }

    .value-card,
    .social-card {
        padding: 2rem 1.5rem;
    }

    .story-content p {
        font-size: 1.1rem;
    }
}
