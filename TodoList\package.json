{"name": "todolist", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@heroicons/react": "^2.2.0", "@react-native-async-storage/async-storage": "^2.2.0", "expo": "~53.0.17", "expo-linear-gradient": "^14.1.5", "expo-status-bar": "~2.2.3", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}