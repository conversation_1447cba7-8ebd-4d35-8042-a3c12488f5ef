/* Contact Page Specific Styles */

/* Contact Header */
.contact-header {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, var(--primary-cream) 0%, var(--warm-gray) 100%);
    text-align: center;
}

.contact-header h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.contact-header p {
    font-size: 1.3rem;
    color: var(--text-medium);
    max-width: 600px;
    margin: 0 auto;
}

/* Contact Content */
.contact-content {
    background: var(--soft-white);
}

.contact-wrapper {
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    gap: 4rem;
    align-items: start;
}

/* Contact Form */
.contact-form-section h2 {
    margin-bottom: 2rem;
    color: var(--text-dark);
}

.contact-form {
    background: var(--primary-cream);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px var(--gentle-shadow);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 2rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid var(--warm-gray);
    border-radius: 10px;
    font-size: 1rem;
    font-family: inherit;
    outline: none;
    transition: all 0.3s ease;
    background: var(--soft-white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--accent-rose);
    box-shadow: 0 0 0 3px rgba(212, 165, 116, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-group select {
    cursor: pointer;
}

/* Checkbox Styling */
.checkbox-group {
    margin-bottom: 1.5rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--warm-gray);
    border-radius: 4px;
    position: relative;
    flex-shrink: 0;
    transition: all 0.3s ease;
    background: var(--soft-white);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--accent-rose);
    border-color: var(--accent-rose);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--soft-white);
    font-size: 12px;
    font-weight: bold;
}

.checkbox-label a {
    color: var(--accent-rose);
    text-decoration: underline;
}

.btn-primary {
    width: 100%;
    padding: 1.2rem;
    font-size: 1.1rem;
    margin-top: 1rem;
}

/* Contact Info */
.contact-info-section h2 {
    margin-bottom: 2rem;
    color: var(--text-dark);
}

.contact-item {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: var(--primary-cream);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px var(--gentle-shadow);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--accent-rose);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--soft-white);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h3 {
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-size: 1.2rem;
}

.contact-details p {
    color: var(--text-dark);
    font-weight: 500;
    margin-bottom: 0.3rem;
}

.contact-details span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Social Media */
.contact-social {
    margin-top: 3rem;
    padding: 2rem;
    background: var(--primary-cream);
    border-radius: 15px;
}

.contact-social h3 {
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.contact-social .social-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-social .social-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem;
    background: var(--soft-white);
    border-radius: 10px;
    text-decoration: none;
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.contact-social .social-link:hover {
    background: var(--accent-rose);
    color: var(--soft-white);
    transform: translateX(5px);
}

.contact-social .social-link i {
    font-size: 1.2rem;
    width: 20px;
}

/* Response Info */
.response-info {
    margin-top: 2rem;
    padding: 2rem;
    background: var(--primary-cream);
    border-radius: 15px;
}

.response-info h3 {
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.response-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 0;
    border-bottom: 1px solid var(--warm-gray);
}

.response-item:last-child {
    border-bottom: none;
}

.response-type {
    color: var(--text-medium);
}

.response-time {
    color: var(--accent-rose);
    font-weight: 500;
}

/* Services */
.services {
    background: var(--warm-gray);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
}

.service-card {
    background: var(--soft-white);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px var(--gentle-shadow);
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px var(--hover-shadow);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-rose), var(--accent-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--soft-white);
    font-size: 2rem;
}

.service-card h3 {
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.service-card p {
    color: var(--text-medium);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-card ul {
    text-align: left;
    margin-bottom: 2rem;
    padding-left: 0;
    list-style: none;
}

.service-card li {
    color: var(--text-medium);
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.service-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-rose);
    font-weight: bold;
}

.service-price {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--accent-rose);
    padding: 1rem;
    background: var(--primary-cream);
    border-radius: 10px;
}

/* FAQ */
.faq {
    background: var(--soft-white);
}

.faq-content {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    margin-bottom: 1.5rem;
    background: var(--primary-cream);
    border-radius: 15px;
    overflow: hidden;
}

.faq-question {
    padding: 1.5rem 2rem;
    margin: 0;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    background: var(--primary-cream);
    border: none;
    width: 100%;
    text-align: left;
    font-size: 1.1rem;
    color: var(--text-dark);
}

.faq-question:hover {
    background: var(--warm-gray);
}

.faq-question::after {
    content: '+';
    position: absolute;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    color: var(--accent-rose);
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question::after {
    transform: translateY(-50%) rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: var(--soft-white);
}

.faq-item.active .faq-answer {
    max-height: 200px;
}

.faq-answer p {
    padding: 1.5rem 2rem;
    margin: 0;
    color: var(--text-medium);
    line-height: 1.6;
}

/* Active navigation link */
.nav-link.active {
    color: var(--accent-rose);
}

.nav-link.active::after {
    width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-header {
        padding: 6rem 0 3rem;
    }

    .contact-header h1 {
        font-size: 2.5rem;
    }

    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-form {
        padding: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .service-card {
        padding: 2rem;
    }

    .contact-social .social-links {
        gap: 0.8rem;
    }
}

@media (max-width: 480px) {
    .contact-header h1 {
        font-size: 2rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .service-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .faq-question {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .faq-answer p {
        padding: 1rem 1.5rem;
    }
}
