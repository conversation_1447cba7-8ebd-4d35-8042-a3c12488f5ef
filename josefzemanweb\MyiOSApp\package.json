{"name": "myiosapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "expo": "~53.0.17", "expo-linear-gradient": "^14.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-game-engine": "^1.2.0", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}