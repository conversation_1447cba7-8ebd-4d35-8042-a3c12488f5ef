/* Blog Page Specific Styles */

/* Blog Header */
.blog-header {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, var(--primary-cream) 0%, var(--warm-gray) 100%);
    text-align: center;
}

.blog-header h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.blog-header p {
    font-size: 1.3rem;
    color: var(--text-medium);
    max-width: 600px;
    margin: 0 auto;
}

/* Blog Filters */
.blog-filters {
    background: var(--soft-white);
    padding: 2rem 0;
    border-bottom: 1px solid var(--warm-gray);
    position: sticky;
    top: 80px;
    z-index: 100;
}

.filters-wrapper {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 2rem;
    align-items: center;
}

.search-box {
    position: relative;
    max-width: 400px;
}

.search-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--warm-gray);
    border-radius: 50px;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: var(--accent-rose);
    box-shadow: 0 0 0 3px rgba(212, 165, 116, 0.1);
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.category-filters {
    display: flex;
    gap: 1rem;
}

.filter-btn {
    padding: 0.8rem 1.5rem;
    border: 2px solid var(--warm-gray);
    background: transparent;
    color: var(--text-medium);
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--accent-rose);
    color: var(--soft-white);
    border-color: var(--accent-rose);
}

.sort-select {
    padding: 0.8rem 1.5rem;
    border: 2px solid var(--warm-gray);
    border-radius: 50px;
    background: var(--soft-white);
    color: var(--text-medium);
    cursor: pointer;
    outline: none;
    font-size: 0.9rem;
}

/* Blog Articles */
.blog-articles {
    background: var(--primary-cream);
    padding: 4rem 0;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 3rem;
}

.article-card {
    background: var(--soft-white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px var(--gentle-shadow);
    transition: all 0.3s ease;
}

.article-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px var(--hover-shadow);
}

.article-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.article-image-wrapper {
    position: relative;
    overflow: hidden;
}

.article-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-card:hover .article-image {
    transform: scale(1.05);
}

.article-category {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--accent-rose);
    color: var(--soft-white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.article-content {
    padding: 2rem;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.reading-time {
    background: var(--warm-gray);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.article-title {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
    line-height: 1.4;
}

.article-excerpt {
    color: var(--text-medium);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.article-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background: var(--warm-gray);
    color: var(--text-medium);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.tag:hover {
    background: var(--accent-rose);
    color: var(--soft-white);
}

/* Load More */
.load-more-wrapper {
    text-align: center;
    margin-top: 4rem;
}

.load-more-btn {
    padding: 1rem 3rem;
    font-size: 1.1rem;
}

/* Search Results */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--soft-white);
    border: 1px solid var(--warm-gray);
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--soft-shadow);
    z-index: 1000;
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.search-result {
    padding: 1rem;
    border-bottom: 1px solid var(--warm-gray);
}

.search-result:last-child {
    border-bottom: none;
}

.search-result h4 {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.search-result p {
    color: var(--text-medium);
    font-size: 0.9rem;
}

/* No Results */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-medium);
}

.no-results h3 {
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.no-results p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .blog-header {
        padding: 6rem 0 3rem;
    }

    .blog-header h1 {
        font-size: 2.5rem;
    }

    .blog-header p {
        font-size: 1.1rem;
    }

    .filters-wrapper {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .category-filters {
        flex-wrap: wrap;
        justify-content: center;
    }

    .articles-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .article-card {
        margin: 0 1rem;
    }

    .filter-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .blog-header h1 {
        font-size: 2rem;
    }

    .article-content {
        padding: 1.5rem;
    }

    .article-title {
        font-size: 1.2rem;
    }

    .category-filters {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }
}

/* Animation for filtered articles */
.article-card.fade-out {
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.article-card.fade-in {
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease;
}

/* Loading animation */
.loading {
    text-align: center;
    padding: 2rem;
    color: var(--text-medium);
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--warm-gray);
    border-top: 2px solid var(--accent-rose);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
