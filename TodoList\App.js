import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  Alert,
  StatusBar,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function App() {
  const [todos, setTodos] = useState([]);
  const [inputText, setInputText] = useState('');
  const [filter, setFilter] = useState('all');

  const addTodo = () => {
    if (inputText.trim() === '') {
      Alert.alert('Chyba', 'Zadejte text úkolu!');
      return;
    }

    const newTodo = {
      id: Date.now().toString(),
      text: inputText.trim(),
      completed: false,
      createdAt: new Date().toISOString(),
    };

    setTodos([newTodo, ...todos]);
    setInputText('');
  };

  const toggleTodo = (id) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const deleteTodo = (id) => {
    Alert.alert(
      'Smazat úkol',
      'Opravdu chcete smazat tento úkol?',
      [
        { text: 'Zrušit', style: 'cancel' },
        { text: 'Smazat', style: 'destructive', onPress: () => {
          setTodos(todos.filter(todo => todo.id !== id));
        }},
      ]
    );
  };

  const clearCompleted = () => {
    const completedCount = todos.filter(todo => todo.completed).length;
    if (completedCount === 0) {
      Alert.alert('Info', 'Žádné dokončené úkoly k smazání.');
      return;
    }

    Alert.alert(
      'Smazat dokončené',
      `Smazat ${completedCount} dokončených úkolů?`,
      [
        { text: 'Zrušit', style: 'cancel' },
        { text: 'Smazat', style: 'destructive', onPress: () => {
          setTodos(todos.filter(todo => !todo.completed));
        }},
      ]
    );
  };

  const getFilteredTodos = () => {
    switch (filter) {
      case 'active':
        return todos.filter(todo => !todo.completed);
      case 'completed':
        return todos.filter(todo => todo.completed);
      default:
        return todos;
    }
  };

  const getStats = () => {
    const total = todos.length;
    const completed = todos.filter(todo => todo.completed).length;
    const active = total - completed;
    return { total, completed, active };
  };

  const renderTodo = ({ item }) => (
    <View style={[styles.todoItem, item.completed && styles.todoCompleted]}>
      <TouchableOpacity
        style={styles.todoContent}
        onPress={() => toggleTodo(item.id)}
        activeOpacity={0.7}
      >
        <View style={[styles.checkbox, item.completed && styles.checkboxCompleted]}>
          {item.completed && <Text style={styles.checkmark}>✓</Text>}
        </View>
        <View style={styles.todoTextContainer}>
          <Text style={[styles.todoText, item.completed && styles.todoTextCompleted]}>
            {item.text}
          </Text>
          <Text style={styles.todoTime}>
            {new Date(item.createdAt).toLocaleDateString('cs-CZ', {
              day: '2-digit',
              month: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => deleteTodo(item.id)}
        activeOpacity={0.7}
      >
        <Text style={styles.deleteButtonText}>🗑️</Text>
      </TouchableOpacity>
    </View>
  );

  const stats = getStats();
  const filteredTodos = getFilteredTodos();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />

      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Text style={styles.headerTitle}>✨ Todo Magic ✨</Text>
        <Text style={styles.headerSubtitle}>
          {stats.total} úkolů • {stats.active} aktivních • {stats.completed} dokončených
        </Text>
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${stats.total > 0 ? (stats.completed / stats.total) * 100 : 0}%`,
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0}% dokončeno
          </Text>
        </View>
      </LinearGradient>

      <View style={styles.inputContainer}>
        <View style={styles.inputWrapper}>
          <Text style={styles.inputIcon}>💭</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Co chcete udělat dnes?"
            placeholderTextColor="#aaa"
            value={inputText}
            onChangeText={setInputText}
            onSubmitEditing={addTodo}
            returnKeyType="done"
            multiline={false}
          />
        </View>
        <TouchableOpacity
          style={styles.addButton}
          onPress={addTodo}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['#4CAF50', '#45a049', '#66BB6A']}
            style={styles.addButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Text style={styles.addButtonText}>✨</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>

      <View style={styles.filterContainer}>
        {[
          { key: 'all', label: '🌟 Všechny', icon: '📋' },
          { key: 'active', label: '⚡ Aktivní', icon: '🔥' },
          { key: 'completed', label: '✅ Hotové', icon: '🎉' },
        ].map((filterType) => (
          <TouchableOpacity
            key={filterType.key}
            style={[styles.filterButton, filter === filterType.key && styles.filterButtonActive]}
            onPress={() => setFilter(filterType.key)}
            activeOpacity={0.8}
          >
            <Text style={styles.filterIcon}>{filterType.icon}</Text>
            <Text style={[styles.filterButtonText, filter === filterType.key && styles.filterButtonTextActive]}>
              {filterType.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <FlatList
        data={filteredTodos}
        renderItem={renderTodo}
        keyExtractor={(item) => item.id}
        style={styles.todoList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>
              {filter === 'all' ? '🌟' : filter === 'active' ? '🎉' : '📝'}
            </Text>
            <Text style={styles.emptyText}>
              {filter === 'all' ? 'Začněte svou cestu!' :
               filter === 'active' ? 'Všechny úkoly dokončeny!' :
               'Žádné dokončené úkoly!'}
            </Text>
            <Text style={styles.emptySubtext}>
              {filter === 'all' ? 'Přidejte svůj první úkol a začněte produktivně!' :
               filter === 'active' ? 'Skvělá práce! Zasloužíte si odpočinek 🏆' :
               'Dokončete nějaké úkoly a uvidíte je zde ✨'}
            </Text>
          </View>
        }
      />

      {stats.completed > 0 && (
        <TouchableOpacity style={styles.clearButton} onPress={clearCompleted}>
          <LinearGradient
            colors={['#FF6B6B', '#FF8E53']}
            style={styles.clearButtonGradient}
          >
            <Text style={styles.clearButtonText}>🗑️ Smazat dokončené ({stats.completed})</Text>
          </LinearGradient>
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f2f5',
  },
  header: {
    paddingTop: 30,
    paddingBottom: 40,
    paddingHorizontal: 25,
    borderBottomLeftRadius: 35,
    borderBottomRightRadius: 35,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 10,
  },
  headerTitle: {
    fontSize: 36,
    fontWeight: '800',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
    marginBottom: 15,
    fontWeight: '500',
  },
  progressContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  progressBar: {
    width: '80%',
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 10,
  },
  progressText: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 14,
    fontWeight: '600',
  },
  inputContainer: {
    flexDirection: 'row',
    paddingHorizontal: 25,
    paddingVertical: 25,
    alignItems: 'center',
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 30,
    paddingHorizontal: 20,
    marginRight: 15,
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  inputIcon: {
    fontSize: 20,
    marginRight: 10,
  },
  textInput: {
    flex: 1,
    paddingVertical: 18,
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  addButton: {
    borderRadius: 30,
    overflow: 'hidden',
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  addButtonGradient: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonText: {
    color: '#fff',
    fontSize: 28,
    fontWeight: 'bold',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 25,
    marginBottom: 20,
  },
  filterButton: {
    flex: 1,
    marginHorizontal: 5,
    borderRadius: 25,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    backgroundColor: '#f8f9fa',
    paddingVertical: 15,
    paddingHorizontal: 10,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#667eea',
  },
  filterIcon: {
    fontSize: 16,
    marginRight: 5,
  },
  filterButtonText: {
    fontSize: 13,
    fontWeight: '700',
    color: '#6c757d',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  todoList: {
    flex: 1,
    paddingHorizontal: 25,
  },
  todoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 20,
    marginBottom: 15,
    paddingHorizontal: 20,
    paddingVertical: 18,
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    borderLeftWidth: 4,
    borderLeftColor: '#667eea',
  },
  todoCompleted: {
    backgroundColor: '#f8f9fa',
    opacity: 0.8,
    borderLeftColor: '#4CAF50',
  },
  todoContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 3,
    borderColor: '#dee2e6',
    marginRight: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxCompleted: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  checkmark: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  todoTextContainer: {
    flex: 1,
  },
  todoText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
    marginBottom: 4,
  },
  todoTextCompleted: {
    textDecorationLine: 'line-through',
    color: '#6c757d',
  },
  todoTime: {
    fontSize: 12,
    color: '#aaa',
    fontWeight: '500',
  },
  deleteButton: {
    borderRadius: 15,
    overflow: 'hidden',
    padding: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButtonText: {
    fontSize: 16,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 30,
  },
  emptyIcon: {
    fontSize: 80,
    marginBottom: 20,
  },
  emptyText: {
    fontSize: 24,
    fontWeight: '800',
    color: '#667eea',
    marginBottom: 15,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 16,
    color: '#adb5bd',
    textAlign: 'center',
    lineHeight: 24,
    fontWeight: '500',
  },
  clearButton: {
    marginHorizontal: 25,
    marginBottom: 25,
    borderRadius: 30,
    overflow: 'hidden',
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  clearButtonGradient: {
    paddingVertical: 18,
    alignItems: 'center',
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
  },
});