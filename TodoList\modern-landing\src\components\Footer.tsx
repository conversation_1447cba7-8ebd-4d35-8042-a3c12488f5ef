'use client';

export default function Footer() {
  const footerLinks = {
    'Produkty': [
      'Osobní finance',
      'Firemní účty',
      'Investice',
      'Kreditní karty',
      'Mobilní aplikace'
    ],
    'Řešení': [
      'Pro jednotlivce',
      'Pro firmy',
      'Pro developery',
      'API dokumentace',
      'Integrace'
    ],
    'Podpora': [
      'Nápověda',
      'Kontakt',
      'Bezpečnost',
      'Status stránky',
      'Komunita'
    ],
    'Společnost': [
      'O nás',
      '<PERSON><PERSON><PERSON>',
      'Tiskové zprávy',
      'Partneři',
      'Blog'
    ]
  };

  const socialLinks = [
    { icon: '📘', href: '#', label: 'Facebook' },
    { icon: '🐦', href: '#', label: 'Twitter' },
    { icon: '📷', href: '#', label: 'Instagram' },
    { icon: '💼', href: '#', label: 'LinkedIn' }
  ];

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Newsletter Section */}
        <div className="py-16 border-b border-gray-800">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold mb-4">
                Zůstaňte v obraze
              </h3>
              <p className="text-gray-400 text-lg">
                Přihlaste se k odběru našeho newsletteru a získejte nejnovější
                informace o funkcích, tipech a finančních trendech.
              </p>
            </div>
            <div>
              <div className="flex flex-col sm:flex-row gap-4">
                <input
                  type="email"
                  placeholder="Váš email"
                  className="flex-1 px-6 py-4 rounded-xl bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-500 transition-colors"
                />
                <button className="bg-emerald-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-emerald-700 hover:scale-105 transition-all flex items-center justify-center group">
                  Přihlásit se
                  <span className="ml-2 group-hover:translate-x-1 transition-transform">→</span>
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-3">
                Žádný spam, pouze užitečné informace. Odhlásit se můžete kdykoli.
              </p>
            </div>
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid lg:grid-cols-6 gap-12">
            {/* Company Info */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-emerald-600 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">F</span>
                </div>
                <span className="text-2xl font-bold">FinanceApp</span>
              </div>
              <p className="text-gray-400 mb-6 leading-relaxed">
                Moderní platforma pro správu financí, která vám pomůže řídit
                vaše peníze chytře a efektivně. Bezpečně, rychle a spolehlivě.
              </p>

              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3 text-gray-400">
                  <span>📧</span>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3 text-gray-400">
                  <span>📞</span>
                  <span>+420 123 456 789</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-400">
                  <span>📍</span>
                  <span>Praha, Česká republika</span>
                </div>
              </div>
            </div>

            {/* Footer Links */}
            {Object.entries(footerLinks).map(([category, links], categoryIndex) => (
              <div key={category}>
                <h4 className="font-bold text-lg mb-6">{category}</h4>
                <ul className="space-y-3">
                  {links.map((link, linkIndex) => (
                    <li key={link}>
                      <a
                        href="#"
                        className="text-gray-400 hover:text-white transition-colors hover:underline"
                      >
                        {link}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="py-8 border-t border-gray-800">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
            {/* Copyright */}
            <div className="text-gray-400 text-center lg:text-left">
              <p>&copy; 2024 FinanceApp. Všechna práva vyhrazena.</p>
              <div className="flex flex-wrap justify-center lg:justify-start gap-6 mt-2">
                <a href="#" className="hover:text-white transition-colors text-sm">
                  Ochrana soukromí
                </a>
                <a href="#" className="hover:text-white transition-colors text-sm">
                  Podmínky použití
                </a>
                <a href="#" className="hover:text-white transition-colors text-sm">
                  Cookies
                </a>
                <a href="#" className="hover:text-white transition-colors text-sm">
                  Právní informace
                </a>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <a
                  key={social.label}
                  href={social.href}
                  className="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-emerald-600 hover:scale-110 transition-all duration-300"
                  aria-label={social.label}
                >
                  <span className="text-xl">{social.icon}</span>
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Floating Back to Top Button */}
        <button
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="fixed bottom-8 right-8 w-14 h-14 bg-emerald-600 rounded-full flex items-center justify-center text-white shadow-lg hover:bg-emerald-700 hover:scale-110 transition-all z-50"
        >
          <span className="text-xl transform -rotate-90">→</span>
        </button>
      </div>
    </footer>
  );
}
