import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ImageBackground,
  Image,
  Animated,
  PanResponder,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

const { width, height } = Dimensions.get('window');

// 🎮 BRUTÁLNÍ 2D GAME ENGINE
const GAME_CONFIG = {
  PLAYER_SIZE: 60,
  GRAVITY: 0.8,
  JUMP_FORCE: -15,
  MOVE_SPEED: 5,
  WORLD_WIDTH: width * 3,
  WORLD_HEIGHT: height,
};

// 📸 VAŠE SKUTEČNÉ FOTKY
const PHOTOS = {
  misa: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=400',
  pepa: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
};

export default function App() {
  // 🎮 HERNÍ STAVY
  const [gameMode, setGameMode] = useState('menu');
  const [currentLevel, setCurrentLevel] = useState(1);
  const [currentCharacter, setCurrentCharacter] = useState('misa');
  const [score, setScore] = useState(0);
  const [lives, setLives] = useState(3);
  
  // 🏃‍♀️ PLAYER PHYSICS
  const playerX = useRef(new Animated.Value(50)).current;
  const playerY = useRef(new Animated.Value(height - 200)).current;
  const velocityY = useRef(0);
  const isGrounded = useRef(true);
  const gameLoop = useRef(null);
  
  // 💎 COLLECTIBLES
  const [collectibles, setCollectibles] = useState([
    { id: 1, x: 200, y: height - 150, type: 'heart', collected: false },
    { id: 2, x: 400, y: height - 200, type: 'star', collected: false },
    { id: 3, x: 600, y: height - 180, type: 'diamond', collected: false },
  ]);

  // 🎮 JOYSTICK CONTROLS
  const joystickPan = useRef(new Animated.ValueXY()).current;
  const joystickResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderMove: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const maxDistance = 40;
      
      if (distance <= maxDistance) {
        joystickPan.setValue({ x: dx, y: dy });
        
        // Move player
        if (Math.abs(dx) > 10) {
          const newX = Math.max(0, Math.min(GAME_CONFIG.WORLD_WIDTH - GAME_CONFIG.PLAYER_SIZE, 
            playerX._value + (dx > 0 ? GAME_CONFIG.MOVE_SPEED : -GAME_CONFIG.MOVE_SPEED)));
          playerX.setValue(newX);
        }
        
        // Jump
        if (dy < -20 && isGrounded.current) {
          velocityY.current = GAME_CONFIG.JUMP_FORCE;
          isGrounded.current = false;
        }
      }
    },
    onPanResponderRelease: () => {
      joystickPan.setValue({ x: 0, y: 0 });
    },
  });

  // 🎮 GAME PHYSICS LOOP
  useEffect(() => {
    if (gameMode === 'playing') {
      gameLoop.current = setInterval(() => {
        // Gravity
        if (!isGrounded.current) {
          velocityY.current += GAME_CONFIG.GRAVITY;
          const newY = Math.min(height - 200, playerY._value + velocityY.current);
          playerY.setValue(newY);
          
          if (newY >= height - 200) {
            isGrounded.current = true;
            velocityY.current = 0;
          }
        }
        
        // Check collectibles
        setCollectibles(prev => prev.map(item => {
          if (!item.collected) {
            const distance = Math.sqrt(
              Math.pow(playerX._value - item.x, 2) + 
              Math.pow(playerY._value - item.y, 2)
            );
            if (distance < 50) {
              setScore(s => s + 100);
              return { ...item, collected: true };
            }
          }
          return item;
        }));
        
        // Check level completion
        const allCollected = collectibles.every(item => item.collected);
        if (allCollected && gameMode === 'playing') {
          setGameMode('victory');
        }
      }, 16);
    }
    
    return () => {
      if (gameLoop.current) {
        clearInterval(gameLoop.current);
      }
    };
  }, [gameMode, collectibles]);

  // 🎮 RENDER MENU
  const renderMenu = () => (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800' }}
      style={styles.background}
      blurRadius={2}
    >
      <LinearGradient
        colors={['rgba(255,20,147,0.9)', 'rgba(138,43,226,0.9)']}
        style={styles.overlay}
      >
        <View style={styles.menuContainer}>
          <Text style={styles.gameTitle}>💕 MÍŠA & PEPA 💕</Text>
          <Text style={styles.gameSubtitle}>Love Quest Adventure</Text>
          
          <View style={styles.characterSelector}>
            <TouchableOpacity
              style={[styles.characterCard, currentCharacter === 'misa' && styles.selectedCard]}
              onPress={() => setCurrentCharacter('misa')}
            >
              <Image source={{ uri: PHOTOS.misa }} style={styles.characterPhoto} />
              <Text style={styles.characterName}>Míša</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.characterCard, currentCharacter === 'pepa' && styles.selectedCard]}
              onPress={() => setCurrentCharacter('pepa')}
            >
              <Image source={{ uri: PHOTOS.pepa }} style={styles.characterPhoto} />
              <Text style={styles.characterName}>Pepa</Text>
            </TouchableOpacity>
          </View>
          
          <TouchableOpacity
            style={styles.playButton}
            onPress={() => {
              setGameMode('playing');
              setScore(0);
              setLives(3);
              playerX.setValue(50);
              playerY.setValue(height - 200);
              setCollectibles(prev => prev.map(item => ({ ...item, collected: false })));
            }}
          >
            <LinearGradient
              colors={['#FF6B6B', '#FF8E53']}
              style={styles.buttonGradient}
            >
              <Text style={styles.playButtonText}>🚀 ZAČÍT LOVE QUEST</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </ImageBackground>
  );

  // 🎮 RENDER GAME
  const renderGame = () => (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800' }}
      style={styles.gameBackground}
    >
      <View style={styles.hud}>
        <Text style={styles.hudText}>💎 {score}</Text>
        <Text style={styles.hudText}>❤️ {lives}</Text>
        <Text style={styles.hudText}>Level {currentLevel}</Text>
      </View>
      
      <Animated.View
        style={[
          styles.player,
          {
            left: playerX,
            top: playerY,
          },
        ]}
      >
        <Image
          source={{ uri: PHOTOS[currentCharacter] }}
          style={styles.playerImage}
        />
      </Animated.View>
      
      {collectibles.map(item => (
        !item.collected && (
          <View
            key={item.id}
            style={[
              styles.collectible,
              { left: item.x, top: item.y }
            ]}
          >
            <Text style={styles.collectibleIcon}>
              {item.type === 'heart' ? '💕' : item.type === 'star' ? '⭐' : '💎'}
            </Text>
          </View>
        )
      ))}
      
      <View style={styles.joystickContainer}>
        <View style={styles.joystickBase}>
          <Animated.View
            style={[
              styles.joystickKnob,
              {
                transform: [
                  { translateX: joystickPan.x },
                  { translateY: joystickPan.y },
                ],
              },
            ]}
            {...joystickResponder.panHandlers}
          />
        </View>
      </View>
      
      <TouchableOpacity
        style={styles.pauseButton}
        onPress={() => setGameMode('menu')}
      >
        <Text style={styles.pauseText}>⏸️</Text>
      </TouchableOpacity>
    </ImageBackground>
  );

  // 🏆 RENDER VICTORY
  const renderVictory = () => (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800' }}
      style={styles.background}
      blurRadius={3}
    >
      <LinearGradient
        colors={['rgba(255,215,0,0.9)', 'rgba(255,105,180,0.9)']}
        style={styles.overlay}
      >
        <View style={styles.victoryContainer}>
          <Text style={styles.victoryTitle}>🎉 LÁSKA ZVÍTĚZILA! 🎉</Text>
          <Text style={styles.victorySubtitle}>Level {currentLevel} dokončen!</Text>
          
          <View style={styles.victoryPhotos}>
            <Image source={{ uri: PHOTOS.misa }} style={styles.victoryPhoto} />
            <Text style={styles.heartIcon}>💕</Text>
            <Image source={{ uri: PHOTOS.pepa }} style={styles.victoryPhoto} />
          </View>
          
          <View style={styles.statsContainer}>
            <Text style={styles.statText}>💎 Skóre: {score}</Text>
            <Text style={styles.statText}>⭐ Level: {currentLevel}</Text>
            <Text style={styles.statText}>💕 Láska: 100%</Text>
          </View>
          
          <TouchableOpacity
            style={styles.nextButton}
            onPress={() => {
              setCurrentLevel(prev => prev + 1);
              setGameMode('menu');
            }}
          >
            <LinearGradient
              colors={['#4CAF50', '#45a049']}
              style={styles.buttonGradient}
            >
              <Text style={styles.nextButtonText}>➡️ DALŠÍ LEVEL</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </ImageBackground>
  );

  const renderCurrentMode = () => {
    switch (gameMode) {
      case 'menu':
        return renderMenu();
      case 'playing':
        return renderGame();
      case 'victory':
        return renderVictory();
      default:
        return renderMenu();
    }
  };

  return renderCurrentMode();
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  gameTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 10,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  gameSubtitle: {
    fontSize: 18,
    color: '#FFD700',
    textAlign: 'center',
    marginBottom: 40,
    fontWeight: '600',
  },
  characterSelector: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 40,
  },
  characterCard: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedCard: {
    borderColor: '#FFD700',
    backgroundColor: 'rgba(255,215,0,0.3)',
  },
  characterPhoto: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 10,
  },
  characterName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  playButton: {
    width: '80%',
    borderRadius: 25,
    overflow: 'hidden',
  },
  buttonGradient: {
    paddingVertical: 15,
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  playButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  gameBackground: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  hud: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    zIndex: 100,
  },
  hudText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
  },
  player: {
    position: 'absolute',
    width: GAME_CONFIG.PLAYER_SIZE,
    height: GAME_CONFIG.PLAYER_SIZE,
    zIndex: 10,
  },
  playerImage: {
    width: '100%',
    height: '100%',
    borderRadius: GAME_CONFIG.PLAYER_SIZE / 2,
    borderWidth: 3,
    borderColor: '#FFD700',
  },
  collectible: {
    position: 'absolute',
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  collectibleIcon: {
    fontSize: 30,
  },
  joystickContainer: {
    position: 'absolute',
    bottom: 50,
    left: 30,
    zIndex: 100,
  },
  joystickBase: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.5)',
  },
  joystickKnob: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFD700',
    borderWidth: 2,
    borderColor: '#fff',
  },
  pauseButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    padding: 10,
    zIndex: 100,
  },
  pauseText: {
    fontSize: 20,
  },
  victoryContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  victoryTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 20,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  victorySubtitle: {
    fontSize: 18,
    color: '#FFD700',
    textAlign: 'center',
    marginBottom: 30,
  },
  victoryPhotos: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
  },
  victoryPhoto: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#FFD700',
  },
  heartIcon: {
    fontSize: 40,
    marginHorizontal: 20,
  },
  statsContainer: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 15,
    padding: 20,
    marginBottom: 30,
    width: '80%',
  },
  statText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 5,
  },
  nextButton: {
    width: '80%',
    borderRadius: 25,
    overflow: 'hidden',
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
