'use client';

export default function CTASection() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Main CTA */}
        <div className="text-center mb-20">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            <PERSON>yz<PERSON><PERSON>šejte si to sami, začněte
            <br />
            <span className="text-emerald-600">za 5 minut</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
            Připojte se k tisícům spokojených uživatelů a začněte řídit své finance chytře už dnes.
            Ž<PERSON><PERSON><PERSON> skryté poplatky, <PERSON><PERSON><PERSON><PERSON>.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <button className="bg-emerald-600 text-white px-10 py-5 rounded-2xl font-bold text-lg hover:bg-emerald-700 hover:scale-105 transition-all duration-300 flex items-center group shadow-lg hover:shadow-xl">
              Začít zdarma
              <span className="ml-3 group-hover:translate-x-1 transition-transform">→</span>
            </button>

            <button className="border-2 border-gray-300 text-gray-700 px-10 py-5 rounded-2xl font-bold text-lg hover:bg-gray-50 hover:scale-105 transition-all duration-300 flex items-center group">
              <span className="mr-3">📱</span>
              Stáhnout aplikaci
            </button>
          </div>

          <p className="text-sm text-gray-500 mt-6">
            Bezplatná registrace • Bez kreditní karty • Zrušit kdykoli
          </p>
        </div>

        {/* App Download Section */}
        <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-3xl p-12 text-white relative overflow-hidden">
          <div className="relative z-10 grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold mb-6">
                Mobilní aplikace pro iOS a Android
              </h3>
              <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                Spravujte své finance kdekoli a kdykoli. Naše mobilní aplikace nabízí
                všechny funkce webové verze s intuitivním dotykovým ovládáním.
              </p>

              <div className="space-y-4 mb-8">
                {[
                  'Biometrické přihlášení',
                  'Push notifikace v reálném čase',
                  'Offline režim pro základní funkce',
                  'Synchronizace napříč zařízeními'
                ].map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <div className="w-2 h-2 bg-emerald-400 rounded-full mr-3"></div>
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-white text-gray-900 px-6 py-3 rounded-xl font-semibold flex items-center justify-center hover:bg-gray-100 hover:scale-105 transition-all">
                  <span className="mr-2">📱</span>
                  App Store
                </button>
                <button className="bg-white text-gray-900 px-6 py-3 rounded-xl font-semibold flex items-center justify-center hover:bg-gray-100 hover:scale-105 transition-all">
                  <span className="mr-2">🖥️</span>
                  Google Play
                </button>
              </div>
            </div>

            <div className="relative">
              <div className="relative animate-bounce"
              >
                {/* Phone mockup */}
                <div className="w-64 h-[500px] bg-gray-700 rounded-[3rem] p-2 mx-auto shadow-2xl">
                  <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
                    {/* Status bar */}
                    <div className="bg-gray-50 px-6 py-3 flex justify-between items-center">
                      <span className="text-sm font-semibold">9:41</span>
                      <div className="flex items-center space-x-1">
                        <div className="w-4 h-2 bg-gray-300 rounded-sm"></div>
                        <div className="w-6 h-3 border border-gray-300 rounded-sm">
                          <div className="w-4 h-full bg-green-500 rounded-sm"></div>
                        </div>
                      </div>
                    </div>
                    
                    {/* App content */}
                    <div className="p-6">
                      <div className="text-center mb-6">
                        <div className="w-16 h-16 bg-emerald-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                          <span className="text-white font-bold text-xl">F</span>
                        </div>
                        <h4 className="font-bold text-gray-900">FinanceApp</h4>
                        <p className="text-sm text-gray-600">Vaše peníze pod kontrolou</p>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="bg-emerald-50 rounded-xl p-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Celkový zůstatek</span>
                            <span className="text-lg font-bold text-emerald-600">124,567 Kč</span>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                          <div className="bg-gray-50 rounded-xl p-3 text-center">
                            <div className="w-8 h-8 bg-blue-500 rounded-lg mx-auto mb-2"></div>
                            <span className="text-xs">Převést</span>
                          </div>
                          <div className="bg-gray-50 rounded-xl p-3 text-center">
                            <div className="w-8 h-8 bg-purple-500 rounded-lg mx-auto mb-2"></div>
                            <span className="text-xs">Investovat</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Dynamic Island */}
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-gray-700 rounded-full"></div>
                </div>
              </div>

              {/* Floating elements */}
              <div className="absolute -top-8 -right-8 w-16 h-16 border-4 border-emerald-400 rounded-full opacity-30 animate-spin-slow" />

              <div className="absolute -bottom-8 -left-8 w-12 h-12 bg-emerald-400 rounded-full opacity-20 animate-pulse" />
            </div>
          </div>

          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-emerald-600 rounded-full opacity-10 -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-blue-600 rounded-full opacity-10 translate-y-24 -translate-x-24"></div>
        </div>
      </div>
    </section>
  );
}
